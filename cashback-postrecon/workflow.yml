workflow:
  id: cashback-postrecon
  name: cashback-postrecon
  description: >-
    Owner: <EMAIL> Purpose: Run Post Processing Part After
    Cashback Recon Flow; is_temporary: false; Created by: <PERSON>; Updated
    by: <PERSON>;
  version: 2
params_profile:
  - name: default
    params:
      - name: recon_config_resource
        value: daily/prod.conf
        dataType: string
      - name: submit_user
        value: hk-fin-ar-prod-svc
        dataType: string
      - name: process_date
        value: '20240822'
        dataType: number
      - name: tag
        value: release
        dataType: string
  - name: dev
    params: []
actions:
  - id: generic-reconciliation-kpi-action
    name: generic-reconciliation-kpi-action
    type: spark
    spec:
      mainApplicationFile: local:///opt/app/assembly/assembly.jar
      image: >-
        reg-sg.agodadev.io/gitlab/financeplatform/reconciliation-platform/reconciliation-platform/cashbackreconciliator
      tag: '{{tag}}'
      arguments:
        - processdate={{ partition_datadate }} workflows=KPI
      additionalSparkConfigurations:
        - key: spark.serializer
          value: org.apache.spark.serializer.KryoSerializer
        - key: spark.hadoop.hadoop.security.credential.provider.path
          value: ''
        - key: spark.kerberos.access.hadoopFileSystems
          value: hdfs://nameservice1,hdfs://nameservice2
        - key: spark.yarn.populateHadoopClasspath
          value: 'true'
        - key: spark.hadoop.smartloader.newtable-vast
          value: 'true'
        - key: spark.queue
          value: production
        - key: spark.shuffle.service.enabled
          value: 'true'
        - key: spark.sql.hive.metastore.token.signature
          value: hiveserver2ClientToken
        - key: spark.yarn.queue
          value: production
        - key: spark.sql.parquet.int96RebaseModeInRead
          value: LEGACY
        - key: spark.hadoop.hive.execution.engine
          value: mr
        - key: spark.sql.hive.metastore.jars
          value: /usr/hdp/current/hive-client/lib/*:./*
        - key: spark.sql.legacy.timeParserPolicy
          value: LEGACY
        - key: spark.sql.sources.partitionOverwriteMode
          value: DYNAMIC
        - key: spark.sql.broadcastTimeout
          value: '1800'
        - key: spark.sql.parquet.writeLegacyFormat
          value: 'true'
        - key: spark.sql.parquet.int96RebaseModeInWrite
          value: LEGACY
        - key: hive.metastore.disallow.incompatible.col.type.changes
          value: 'false'
        - key: spark.sql.hive.metastore.version
          value: 3.1.1
        - key: spark.eventLog.enabled
          value: 'true'
        - key: spark.storage.memoryMapThreshold
          value: '2097152'
        - key: spark.yarn.maxAppAttempts
          value: '1'
      sparkVersion: 3.3.1
      mainClass: com.agoda.finance.cashbackreconciliator.Main
      driver:
        memoryOverhead: 16384m
        javaOptions:
          - '-XX:+CMSClassUnloadingEnabled'
          - '-XX:+HeapDumpOnOutOfMemoryError'
          - '-XX:HeapDumpPath=/agoda/heap_dump/'
          - >-
            -Dhive.metastore.filter.hook=com.agoda.adp.hadoop.hive.client.hook.AdpFilterHook
          - '-Dconfig.strategy=com.agoda.ml.config.MultiResourceStrategy'
          - '-Dconfig.resource=''{{recon_config_resource}}'''
          - '-Dlog4j.configuration=log4j_adp.properties'
          - '-Dhadoop.spark.submituser=root'
        memory: 20g
      dynamicAllocation: {}
      executor:
        instances: '10'
        cores: '5'
        memory: 10gb
        memoryOverhead: 1024m
        javaOptions:
          - '-XX:+CMSClassUnloadingEnabled'
          - '-XX:+HeapDumpOnOutOfMemoryError'
          - '-XX:HeapDumpPath=/agoda/heap_dump/'
          - '-Dconfig.strategy=com.agoda.ml.config.MultiResourceStrategy'
          - '-Dconfig.resource=''{{recon_config_resource}}'''
          - '-Dlog4j.configuration=log4j_adp.properties'
          - '-Dhadoop.spark.submituser=root'
      dependencies:
        jars:
          - local:///opt/app/assembly/dependency_cache.jar
        files:
          - local:///opt/app/deps/files/log4j_adp.properties
    retryInterval: 1
    dependsOn: []
schedules: []
