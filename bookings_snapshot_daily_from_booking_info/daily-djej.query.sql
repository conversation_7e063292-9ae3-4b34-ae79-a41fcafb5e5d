SELECT booking_id
	,cid
	,payment_model
	,confirmation_date
	,booking_date
	,start_date
	,end_date
	,cancellation_date
	,supplier_id
	,sub_supplier_id
	,service_origin_iso3
	,merchant_of_record_entity
	,revenue_entity
	,rate_contract_entity
	,whitelabel_id
	,is_test
	,is_advance_guarantee
	,whitelabel_group_id
	,charge_option_id
	,sell_tag_id
	,original_bnpl_fully_charge_date
	,original_bnpl_fully_auth_date
	,contract_type_id
	,rate_channel
	,affiliate_id
	,affiliate_model_name as affiliate_model
	,program_name
    ,cast(date_format(current_date(), "yyyyMMdd") AS INT) as datadate
FROM finance_dw.booking_info