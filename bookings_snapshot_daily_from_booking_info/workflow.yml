workflow:
  id: bookings_snapshot_daily_from_booking_info
  name: bookings_snapshot_daily_from_booking_info
  description: ''
  version: 2
actions:
  - id: daily-djej
    name: bookings
    type: h2h
    spec:
      query: daily-djej.query.sql
      targetTable: bookings_snapshot
      targetDatabase: '{{finance_multiproduct_scr_daily_db}}'
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
      alterTableSchema: true
      maxDuration: 900
      additionalSparkConfigurations: []
    dependsOn: []
    maxRetry: '3'
    retryInterval: '10'
params_profile:
  - name: default
    params:
      - name: finance_multiproduct_scr_daily_db
        value: finance_multiproduct_scr_daily
        dataType: string
  - name: dev
    params:
      - name: finance_multiproduct_scr_daily_db
        value: finance_multiproduct_scr_daily_dev
        dataType: string
schedules:
  - id: daily-jkfl
    name: daily
    timezone: UTC
    cron: 30 17 * * *
    parameters: []
    timeout: 120
    dataDependencies:
      - table: finance_dw.booking_info
        column: created_log_time
        method: '>='
        offset: -30
        format: Minute
