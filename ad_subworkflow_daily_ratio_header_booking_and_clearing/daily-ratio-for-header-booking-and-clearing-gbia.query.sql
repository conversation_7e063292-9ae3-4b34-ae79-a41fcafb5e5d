WITH details AS (
    SELECT sum(t.ebe_usd_amount) AS usd_amount
         , {{hive_yesterday_datadate}} AS datadate
    FROM Agoda_Finance.dbo.detail AS t
     INNER JOIN Agoda_Finance.dbo.header AS h ON h.header_id = t.header_id AND t.item_id = h.item_id_use
     INNER JOIN Agoda_Finance.dbo.header_booking AS hb ON hb.header_id = t.header_id
    WHERE t.rec_created_when >= '{{from_accounting_date}}'
      AND t.rec_created_when < '{{to_accounting_date}}'
      AND hb.is_payable = 1
),
clearing_details AS (
    SELECT sum(cd.usd_total_include_gst) AS usd_amount
         , {{hive_yesterday_datadate}} AS datadate
    FROM Agoda_Finance.dbo.clearing_detail AS cd
    WHERE cd.created_when >= '{{from_accounting_date}}'
      AND cd.created_when < '{{to_accounting_date}}'
      AND cd.clearing_type_id = 1 AND cd.clearing_provider_id = 1
)
SELECT 'USD' as currency
     , CASE WHEN SUM(cd.usd_amount) = 0 THEN 0 ELSE (SUM(d.usd_amount) / SUM(cd.usd_amount)) END AS ratio_usd
     , ABS(SUM(d.usd_amount) - SUM(cd.usd_amount)) AS diff_usd
     , {{hive_yesterday_datadate}} AS datadate
    FROM details AS d
    INNER JOIN clearing_details AS cd ON d.datadate = cd.datadate
WHERE $CONDITIONS