workflow:
  id: ad_subworkflow_daily_ratio_header_booking_and_clearing
  name: ad_subworkflow_daily_ratio_header_booking_and_clearing
  description: ''
  version: 2
actions:
  - id: daily-ratio-for-header-booking-and-clearing-gbia
    name: daily_ratio_for_header_booking_and_clearing
    type: s2h
    spec:
      query: daily-ratio-for-header-booking-and-clearing-gbia.query.sql
      targetTable: '{{resolve_table_name("daily_ratio_for_header_booking_and_clearing")}}'
      targetDatabase: finance_metrics
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
      sqlConnection:
        id: sg-dwsec_-_agoda_finance_-_finance_platform_rw_user
    dependsOn: []
params_profile:
  - name: default
    params:
      - name: from_accounting_date
        value: __deferred
        dataType: date
      - name: to_accounting_date
        value: __deferred
        dataType: date
      - name: hive_yesterday_datadate
        value: __deferred
        dataType: date
  - name: dev
    params: []
schedules: []
