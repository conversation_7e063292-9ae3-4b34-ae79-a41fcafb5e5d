workflow:
  id: cashback-reconciliator
  name: cashback-reconciliator
  description: ''
  version: 2
params_profile:
  - name: default
    params:
      - name: recon_config_resource
        value: daily/prod.conf
        dataType: string
      - name: cashback_wise_jar_downloader_config_resource
        value: instances/cashback_wise_jar_file/prod.conf
        dataType: string
      - name: compensation_wise_jar_downloader_config_resource
        value: instances/compensation_wise_jar_file/prod.conf
        dataType: string
      - name: normalizer_config_resource
        value: cashback_normalized_data/prod.conf
        dataType: string
      - name: process_date
        value: __deferred
        dataType: date
      - name: normalizer_mode
        value: prod
        dataType: string
      - name: mifinity_downloader_config_resource
        value: instances/cashback_mifinity_file/prod.conf
        dataType: string
      - name: tag
        value: release
        dataType: string
  - name: dev
    params:
      - name: recon_config_resource
        value: daily/staging.conf
        dataType: string
      - name: cashback_wise_jar_downloader_config_resource
        value: instances/cashback_wise_jar_file/staging.conf
        dataType: string
      - name: compensation_wise_jar_downloader_config_resource
        value: instances/compensation_wise_jar_file/staging.conf
        dataType: string
      - name: normalizer_config_resource
        value: cashback_normalized_data/staging.conf
        dataType: string
      - name: mifinity_downloader_config_resource
        value: instances/cashback_mifinity_file/staging.conf
        dataType: string
actions:
  - id: mifinity-downloader
    name: mifinity_downloader
    type: spark
    spec:
      mainApplicationFile: local:///opt/app/assembly/assembly.jar
      image: >-
        reg-sg.agodadev.io/gitlab/financeplatform/reconciliation-platform/external-data-pipeline/downloader
      tag: '{{tag}}'
      arguments: &ref_0
        - mode=prod
      additionalSparkConfigurations: &ref_1
        - key: spark.serializer
          value: org.apache.spark.serializer.KryoSerializer
        - key: spark.hadoop.hadoop.security.credential.provider.path
          value: ''
        - key: spark.kerberos.access.hadoopFileSystems
          value: hdfs://nameservice1,hdfs://nameservice2
        - key: spark.yarn.populateHadoopClasspath
          value: 'true'
        - key: spark.hadoop.smartloader.newtable-vast
          value: 'true'
        - key: spark.queue
          value: production
        - key: spark.shuffle.service.enabled
          value: 'true'
        - key: spark.sql.hive.metastore.token.signature
          value: hiveserver2ClientToken
        - key: spark.yarn.queue
          value: production
        - key: spark.sql.parquet.int96RebaseModeInRead
          value: LEGACY
        - key: spark.hadoop.hive.execution.engine
          value: mr
        - key: spark.sql.hive.metastore.jars
          value: /usr/hdp/current/hive-client/lib/*:./*
        - key: spark.sql.legacy.timeParserPolicy
          value: EXCEPTION
        - key: spark.sql.sources.partitionOverwriteMode
          value: DYNAMIC
        - key: spark.sql.broadcastTimeout
          value: '1800'
        - key: spark.sql.parquet.writeLegacyFormat
          value: 'true'
        - key: spark.sql.parquet.int96RebaseModeInWrite
          value: LEGACY
        - key: hive.metastore.disallow.incompatible.col.type.changes
          value: 'false'
        - key: spark.sql.hive.metastore.version
          value: 3.1.1
        - key: spark.eventLog.enabled
          value: 'true'
        - key: spark.storage.memoryMapThreshold
          value: '2097152'
        - key: spark.yarn.maxAppAttempts
          value: '1'
      sparkVersion: 3.3.1
      mainClass: com.agoda.finance.Main
      driver:
        memoryOverhead: 16384m
        javaOptions:
          - '-XX:+CMSClassUnloadingEnabled'
          - '-XX:+HeapDumpOnOutOfMemoryError'
          - '-XX:HeapDumpPath=/agoda/heap_dump/'
          - >-
            -Dhive.metastore.filter.hook=com.agoda.adp.hadoop.hive.client.hook.AdpFilterHook
          - '-Dconfig.strategy=com.agoda.ml.config.MultiResourceStrategy'
          - '-Dconfig.resource=''{{mifinity_downloader_config_resource}}'''
          - '-Dlog4j.configuration=log4j_adp.properties'
          - '-Dhadoop.spark.submituser=root'
        memory: 20g
      dynamicAllocation: &ref_2 {}
      executor:
        instances: '10'
        cores: '5'
        memory: 10gb
        memoryOverhead: 1024m
        javaOptions:
          - '-XX:+CMSClassUnloadingEnabled'
          - '-XX:+HeapDumpOnOutOfMemoryError'
          - '-XX:HeapDumpPath=/agoda/heap_dump/'
          - '-Dconfig.strategy=com.agoda.ml.config.MultiResourceStrategy'
          - '-Dconfig.resource=''{{mifinity_downloader_config_resource}}'''
          - '-Dlog4j.configuration=log4j_adp.properties'
          - '-Dhadoop.spark.submituser=root'
      dependencies: &ref_3
        jars:
          - local:///opt/app/assembly/dependency_cache.jar
        files:
          - local:///opt/app/deps/files/log4j_adp.properties
    dependsOn:
      - fork-bjak
    maxRetry: '3'
    retryInterval: '1'
  - id: compensation-wise-jar-downloader
    name: compensation_wise_jar_downloader
    type: spark
    spec:
      mainApplicationFile: local:///opt/app/assembly/assembly.jar
      image: >-
        reg-sg.agodadev.io/gitlab/financeplatform/reconciliation-platform/external-data-pipeline/downloader
      tag: '{{tag}}'
      arguments: *ref_0
      additionalSparkConfigurations: *ref_1
      sparkVersion: 3.3.1
      mainClass: com.agoda.finance.Main
      driver:
        memoryOverhead: 16384m
        javaOptions:
          - '-XX:+CMSClassUnloadingEnabled'
          - '-XX:+HeapDumpOnOutOfMemoryError'
          - '-XX:HeapDumpPath=/agoda/heap_dump/'
          - >-
            -Dhive.metastore.filter.hook=com.agoda.adp.hadoop.hive.client.hook.AdpFilterHook
          - '-Dconfig.strategy=com.agoda.ml.config.MultiResourceStrategy'
          - '-Dconfig.resource=''{{compensation_wise_jar_downloader_config_resource}}'''
          - '-Dlog4j.configuration=log4j_adp.properties'
          - '-Dhadoop.spark.submituser=root'
        memory: 20g
      dynamicAllocation: *ref_2
      executor:
        instances: '10'
        cores: '5'
        memory: 10gb
        memoryOverhead: 1024m
        javaOptions:
          - '-XX:+CMSClassUnloadingEnabled'
          - '-XX:+HeapDumpOnOutOfMemoryError'
          - '-XX:HeapDumpPath=/agoda/heap_dump/'
          - '-Dconfig.strategy=com.agoda.ml.config.MultiResourceStrategy'
          - '-Dconfig.resource=''{{compensation_wise_jar_downloader_config_resource}}'''
          - '-Dlog4j.configuration=log4j_adp.properties'
          - '-Dhadoop.spark.submituser=root'
      dependencies: *ref_3
    dependsOn:
      - fork-bjak
    maxRetry: '3'
    retryInterval: '1'
  - id: wise-jar-downloader
    name: wise_jar_downloader
    type: spark
    spec:
      mainApplicationFile: local:///opt/app/assembly/assembly.jar
      image: >-
        reg-sg.agodadev.io/gitlab/financeplatform/reconciliation-platform/external-data-pipeline/downloader
      tag: '{{tag}}'
      arguments: *ref_0
      additionalSparkConfigurations: *ref_1
      sparkVersion: 3.3.1
      mainClass: com.agoda.finance.Main
      driver:
        memoryOverhead: 16384m
        javaOptions:
          - '-XX:+CMSClassUnloadingEnabled'
          - '-XX:+HeapDumpOnOutOfMemoryError'
          - '-XX:HeapDumpPath=/agoda/heap_dump/'
          - >-
            -Dhive.metastore.filter.hook=com.agoda.adp.hadoop.hive.client.hook.AdpFilterHook
          - '-Dconfig.strategy=com.agoda.ml.config.MultiResourceStrategy'
          - '-Dconfig.resource=''{{cashback_wise_jar_downloader_config_resource}}'''
          - '-Dlog4j.configuration=log4j_adp.properties'
          - '-Dhadoop.spark.submituser=root'
        memory: 20g
      dynamicAllocation: *ref_2
      executor:
        instances: '10'
        cores: '5'
        memory: 10gb
        memoryOverhead: 1024m
        javaOptions:
          - '-XX:+CMSClassUnloadingEnabled'
          - '-XX:+HeapDumpOnOutOfMemoryError'
          - '-XX:HeapDumpPath=/agoda/heap_dump/'
          - '-Dconfig.strategy=com.agoda.ml.config.MultiResourceStrategy'
          - '-Dconfig.resource=''{{cashback_wise_jar_downloader_config_resource}}'''
          - '-Dlog4j.configuration=log4j_adp.properties'
          - '-Dhadoop.spark.submituser=root'
      dependencies: *ref_3
    dependsOn:
      - fork-bjak
    maxRetry: '3'
    retryInterval: '1'
  - id: normalizer
    name: normalizer
    type: spark
    spec:
      mainApplicationFile: local:///opt/app/assembly/assembly.jar
      image: >-
        reg-sg.agodadev.io/gitlab/financeplatform/reconciliation-platform/external-data-pipeline/normalizer
      tag: '{{tag}}'
      arguments:
        - >-
          mode={{normalizer_mode}} processdate={{process_date}} partitiondate={{process_date}} savemode=append
      additionalSparkConfigurations:
        - key: spark.serializer
          value: org.apache.spark.serializer.KryoSerializer
        - key: spark.hadoop.hadoop.security.credential.provider.path
          value: ''
        - key: spark.kerberos.access.hadoopFileSystems
          value: hdfs://nameservice1,hdfs://nameservice2
        - key: spark.yarn.populateHadoopClasspath
          value: 'true'
        - key: spark.hadoop.smartloader.newtable-vast
          value: 'true'
        - key: spark.queue
          value: production
        - key: spark.shuffle.service.enabled
          value: 'true'
        - key: spark.sql.hive.metastore.token.signature
          value: hiveserver2ClientToken
        - key: spark.yarn.queue
          value: production
        - key: spark.sql.parquet.int96RebaseModeInRead
          value: LEGACY
        - key: spark.hadoop.hive.execution.engine
          value: mr
        - key: spark.sql.hive.metastore.jars
          value: /usr/hdp/current/hive-client/lib/*:./*
        - key: spark.sql.sources.partitionOverwriteMode
          value: DYNAMIC
        - key: spark.sql.broadcastTimeout
          value: '1800'
        - key: spark.sql.parquet.writeLegacyFormat
          value: 'true'
        - key: spark.sql.parquet.int96RebaseModeInWrite
          value: LEGACY
        - key: hive.metastore.disallow.incompatible.col.type.changes
          value: 'false'
        - key: spark.sql.hive.metastore.version
          value: 3.1.1
        - key: spark.eventLog.enabled
          value: 'true'
        - key: spark.storage.memoryMapThreshold
          value: '2097152'
        - key: spark.yarn.maxAppAttempts
          value: '1'
      sparkVersion: 3.3.1
      mainClass: com.agoda.finance.Main
      driver:
        memoryOverhead: 16384m
        javaOptions:
          - '-XX:+CMSClassUnloadingEnabled'
          - '-XX:+HeapDumpOnOutOfMemoryError'
          - '-XX:HeapDumpPath=/agoda/heap_dump/'
          - >-
            -Dhive.metastore.filter.hook=com.agoda.adp.hadoop.hive.client.hook.AdpFilterHook
          - '-Dconfig.strategy=com.agoda.ml.config.MultiResourceStrategy'
          - '-Dconfig.resource=''{{normalizer_config_resource}}'''
          - '-Dlog4j.configuration=log4j_adp.properties'
          - '-Dhadoop.spark.submituser=root'
        memory: 20g
      dynamicAllocation: {}
      executor:
        instances: '15'
        cores: '5'
        memory: 20g
        memoryOverhead: 16384m
        javaOptions:
          - '-XX:+CMSClassUnloadingEnabled'
          - '-XX:+HeapDumpOnOutOfMemoryError'
          - '-XX:HeapDumpPath=/agoda/heap_dump/'
          - '-Dconfig.strategy=com.agoda.ml.config.MultiResourceStrategy'
          - '-Dconfig.resource=''{{normalizer_config_resource}}'''
          - '-Dlog4j.configuration=log4j_adp.properties'
          - '-Dhadoop.spark.submituser=root'
      dependencies:
        jars:
          - local:///opt/app/assembly/dependency_cache.jar
        files:
          - local:///opt/app/deps/files/log4j_adp.properties
    retryInterval: 1
    dependsOn:
      - join-lkik
    maxRetry: '3'
  - id: fork-bjak
    name: fork-bjak
    type: fork
    dependsOn: []
  - id: join-lkik
    name: join-lkik
    type: join
    dependsOn:
      - mifinity-downloader
      - wise-jar-downloader
      - compensation-wise-jar-downloader
schedules:
  - id: daily-680d
    name: daily
    timezone: Asia/Bangkok
    cron: 5 0 * * *
    parameters:
      - name: process_date
        value:
          dateFormat: yyyyMMdd
          offsets: []
        dataType: date
    dataDependencies:
      - table: finance_multiproduct.financial_transactions
        column: daily_datetime
        method: '>='
        offset: -1
        format: Day
    timeout: 60
