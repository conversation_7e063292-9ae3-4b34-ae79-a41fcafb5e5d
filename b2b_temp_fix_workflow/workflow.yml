workflow:
  id: b2b_temp_fix_workflow
  name: B2B_Temp_Fix_Workflow
  description: ''
  version: 2
actions:
  - id: subworkflow-343e
    name: B2B Processor (subworkflow-343e)
    type: sub-workflow
    spec:
      workflowPath: >-
        git://etl-workflows/wf-service-accounts/hk-fin-ar-prod-svc/b2b-processor-daily-workflow-byworkflow-xml#main
      subWorkflowProperties:
        - key: submitUser
          value: hk-fin-ar-prod-svc
        - key: hive_today_date
          value: '{{hive_today_date}}'
      propagateConfigurations: true
    dependsOn: []
params_profile:
  - name: default
    params:
      - name: hive_today_date
        value: >-
          ${coord:formatTime(coord:dateOffset(coord:nominalTime(), 0, 'DAY'),
          'yyyyMMdd')}
        dataType: string
      - name: env
        value: 'production'
        dataType: string
      - name: email
        value: <EMAIL>
        dataType: string
      - name: configResource
        value: 'sg/release.conf'
        dataType: string
  - name: dev
    params: []
schedules: []
