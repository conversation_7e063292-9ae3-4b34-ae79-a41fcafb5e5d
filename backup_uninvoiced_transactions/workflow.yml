workflow:
  id: backup_uninvoiced_transactions
  name: backup_uninvoiced_transactions
  description: ''
  version: 2
actions:
  - id: uninvoiced-transactions-92a8
    name: uninvoiced_transactions
    type: h2h
    spec:
      query: uninvoiced_transactions-92a8.query.sql
      targetTable: '{{target_table}}'
      targetDatabase: finance_b2b
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn: []
params_profile:
  - name: default
    params:
      - name: datadate
        value: __deferred
        dataType: number
      - name: target_table
        value: __deferred
        dataType: string
  - name: dev
    params: []
schedules: []
