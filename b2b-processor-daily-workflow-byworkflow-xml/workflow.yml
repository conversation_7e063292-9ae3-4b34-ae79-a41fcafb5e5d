workflow:
  id: b2b-processor-daily-workflow-byworkflow-xml
  name: B2B_Processor_Daily_Workflow-byWorkflow_
  description: >-
    Owner: <EMAIL>; Purpose: sub workflow for b2b daily
    invalid, valid, uninvoiced transaction calculation; Coordinator_names: B2B
    Daily UnInvoice and Daily Booking; is_temporary: No; Created by:
    ayon.bhat<PERSON><PERSON>@agoda.com; Updated by: <EMAIL>;
  version: 2
params_profile:
  - name: default
    params:
      - name: env
        value: __deferred
        dataType: string
      - name: configResource
        value: __deferred
        dataType: string
      - name: submitUser
        value: __deferred
        dataType: string
      - name: hive_today_date
        value: __deferred
        dataType: string
      - name: invoice_processor_path
        value: __deferred
        dataType: string
      - name: tag
        value: release
        dataType: string
  - name: dev
    params:
      - name: env
        value: __deferred
        dataType: string
      - name: configResource
        value: __deferred
        dataType: string
      - name: submitUser
        value: __deferred
        dataType: string
      - name: hive_today_date
        value: __deferred
        dataType: string
      - name: tag
        value: __deferred
        dataType: string
actions:
  - id: generic-da4b
    name: generic-da4b
    type: spark
    dependsOn: []
    maxRetry: 0
    retryInterval: 1
    spec:
      mainApplicationFile: local:///opt/app/assembly/assembly.jar
      image: >-
        reg-sg.agodadev.io/gitlab/financeplatform/core-platform/invoice-data-processor/b2b-processor
      tag: '{{tag}}'
      additionalSparkConfigurations:
        - key: spark.executor.java-opts
          value: >-
            -XX:+CMSClassUnloadingEnabled -XX:+HeapDumpOnOutOfMemoryError
            -XX:HeapDumpPath=/agoda/heap_dump/
            -Dhadoop.postgres.connectionString=********************************************************
            -Detl.services.refresh.server-location=PROD_SGP
        - key: spark.serializer
          value: org.apache.spark.serializer.KryoSerializer
        - key: spark.hadoop.fs.s3a.experimental.input.fadvise
          value: random
        - key: spark.hadoop.hadoop.security.credential.provider.path
          value: ''
        - key: spark.kerberos.access.hadoopFileSystems
          value: hdfs://nameservice1,hdfs://nameservice2
        - key: spark.sql.autoBroadcastJoinThreshold
          value: 50M
        - key: spark.eventLog.enabled
          value: 'true'
        - key: spark.yarn.populateHadoopClasspath
          value: 'true'
        - key: spark.driver.maxResultSize
          value: 5g
        - key: spark.hadoop.fs.s3a.committer.staging.unique-filenames
          value: 'true'
        - key: spark.hadoop.fs.s3a.committer.name
          value: directory
        - key: spark.queue
          value: production
        - key: spark.shuffle.service.enabled
          value: 'true'
        - key: spark.hadoop.fs.hdfs.impl.disable.cache
          value: 'true'
        - key: spark.sql.hive.metastore.token.signature
          value: hiveserver2ClientToken
        - key: spark.yarn.queue
          value: production
        - key: spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version
          value: '2'
        - key: spark.kryoserializer.buffer.max
          value: 1G
        - key: spark.hadoop.fs.s3a.committer.staging.abort.pending.uploads
          value: 'true'
        - key: spark.hadoop.fs.s3a.path.style.access
          value: 'true'
        - key: spark.hadoop.hive.execution.engine
          value: mr
        - key: spark.sql.hive.metastore.jars
          value: /usr/hdp/current/hive-client/lib/*:./*
        - key: spark.hadoop.fs.s3a.aws.credentials.provider
          value: com.agoda.adp.security.s3.AgodaS3CredentialsProvider
        - key: spark.storage.memoryMapThreshold
          value: '2097152'
        - key: spark.logConf
          value: 'true'
        - key: spark.sql.codegen.wholeStage
          value: 'false'
        - key: spark.hadoop.fs.s3a.acl.default
          value: AuthenticatedRead
        - key: spark.hadoop.fs.s3a.connection.establish.timeout
          value: '5000'
        - key: spark.sql.legacy.timeParserPolicy
          value: LEGACY
        - key: spark.sql.hive.caseSensitiveInferenceMode
          value: NEVER_INFER
        - key: spark.sql.sources.partitionOverwriteMode
          value: static
        - key: spark.hadoop.fs.AbstractFileSystem.s3a.impl
          value: alluxio.hadoop.AbstractUnifiedFileSystem
        - key: spark.sql.broadcastTimeout
          value: '1800'
        - key: spark.hadoop.mapreduce.fileoutputcommitter.marksuccessfuljobs
          value: 'false'
        - key: spark.yarn.maxAppAttempts
          value: '1'
        - key: spark.sql.parquet.output.committer.class
          value: org.apache.spark.internal.io.cloud.BindingParquetOutputCommitter
        - key: spark.hadoop.fs.s3a.committer.magic.enabled
          value: 'false'
        - key: spark.hadoop.fs.s3a.fast.upload.buffer
          value: bytebuffer
        - key: spark.hadoop.fs.s3a.committer.staging.conflict-mode
          value: replace
        - key: spark.sql.sources.commitProtocolClass
          value: org.apache.spark.internal.io.cloud.PathOutputCommitProtocol
        - key: spark.hadoop.mapreduce.outputcommitter.factory.scheme.s3a
          value: org.apache.hadoop.fs.s3a.commit.S3ACommitterFactory
        - key: spark.sql.parquet.writeLegacyFormat
          value: 'true'
        - key: spark.yarn.driver.memoryOverhead
          value: '8192'
        - key: spark.hadoop.mapreduce.fileoutputcommitter.cleanup-failures.ignored
          value: 'true'
        - key: spark.hadoop.fs.s3a.impl
          value: alluxio.hadoop.UnifiedFileSystem
        - key: hive.metastore.disallow.incompatible.col.type.changes
          value: 'false'
        - key: spark.driver.java-opts
          value: >-
            -XX:+CMSClassUnloadingEnabled -XX:+HeapDumpOnOutOfMemoryError
            -XX:HeapDumpPath=/agoda/heap_dump/
            -Dhadoop.postgres.connectionString=********************************************************
            -Detl.services.refresh.server-location=PROD_SGP
        - key: spark.sql.shuffle.partitions
          value: '400'
        - key: spark.sql.hive.metastore.version
          value: 3.1.1
      sparkVersion: 3.3.1
      mainClass: com.agoda.finance.b2bprocessor.Main
      driver:
        memoryOverhead: 5120m
        javaOptions:
          - '-XX:+CMSClassUnloadingEnabled'
          - '-XX:+HeapDumpOnOutOfMemoryError'
          - '-XX:HeapDumpPath=/agoda/heap_dump/'
          - >-
            -Dhive.metastore.filter.hook=com.agoda.adp.hadoop.hive.client.hook.AdpFilterHook
          - '-Dconfig.strategy=com.agoda.ml.config.MultiResourceStrategy'
          - '-Dconfig.resource={{configResource}}'
          - '-Dlog4j.configuration=log4j_adp.properties'
          - '-Dhadoop.spark.submituser={{submitUser}}'
        memory: 25g
      dynamicAllocation: {}
      executor:
        instances: '24'
        cores: '5'
        memory: 15g
        memoryOverhead: 5120m
        javaOptions:
          - '-XX:+CMSClassUnloadingEnabled'
          - '-XX:+HeapDumpOnOutOfMemoryError'
          - '-XX:HeapDumpPath=/agoda/heap_dump/'
          - '-Dconfig.strategy=com.agoda.ml.config.MultiResourceStrategy'
          - '-Dconfig.resource={{configResource}}'
          - '-Dlog4j.configuration=log4j_adp.properties'
          - '-Dhadoop.spark.submituser={{submitUser}}'
      dependencies:
        jars:
          - local:///opt/app/assembly/dependency_cache.jar
      arguments:
        - processdate={{hive_today_date}}
schedules: []
