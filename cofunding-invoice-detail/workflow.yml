workflow:
  id: cofunding-invoice-detail
  name: Cofunding invoice detail
  description: ''
  version: 2
actions:
  - id: read-sftp-ajdj
    name: Read SFTP
    type: import-from-sftp
    spec:
      targetTable: invoice_detail_raw
      targetDatabase: '{{database}}'
      partitionBy:
        staticValue:
          columns:
            - name: datadate
              type: int
              value: '{{datadate}}'
      mergeBy:
        overwritePartition: {}
      connection:
        id: etl_-_sftp_-_-_-_erp_service_user
      failOnNoFiles: false
      source:
        remotePath: '{{file_path}}'
        filePattern: AGD_AR_COFUNDING_CUSTOMER_{{datadate}}.csv
        isRecursive: false
        fileFormat:
          csv:
            delimiter: ','
            quote: '"'
        schemaFile: read-sftp-ajdj.schema.json
    dependsOn: []
  - id: write-master-table-kibb
    name: Write Master Table
    type: h2h
    spec:
      query: write-master-table-kibb.query.sql
      targetTable: invoice_detail
      targetDatabase: '{{database}}'
      partitionBy:
        staticValue:
          columns:
            - name: master
              type: string
              value: master
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - read-sftp-ajdj
params_profile:
  - name: default
    params:
      - name: database
        value: finance_cofunding
        dataType: string
      - name: datadate
        value: __deferred
        dataType: date
      - name: file_path
        value: /OracleFinance/Outbound/INT031_AR_Cofunding_Customer/
        dataType: string
  - name: dev
    params:
      - name: database
        value: finance_cofunding_uat
        dataType: string
      - name: file_path
        value: /OracleFinance/Outbound/INT031_AR_Cofunding_Customer/IT_TEST/
        dataType: string
schedules:
  - id: daily-chcd
    name: Daily
    timezone: UTC
    cron: 59 16 * * *
    parameters:
      - dataType: date
        name: datadate
        value:
          dateFormat: yyyyMMdd
          offsets: []
