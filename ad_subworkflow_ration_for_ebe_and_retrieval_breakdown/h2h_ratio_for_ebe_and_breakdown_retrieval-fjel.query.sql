SELECT 
      {{hive_date_format}} as datadate
    , 'USD' as currency
    , CASE
        WHEN fb.total_usd_amount = 0.00 THEN 0
        ELSE d.total_usd_amount / fb.total_usd_amount
      END AS ratio_usd
    , ABS(d.total_usd_amount - fb.total_usd_amount) AS diff_usd
FROM finance_metrics.{{resolve_table_name("s2h_breakdown_retrieval")}} AS d
INNER JOIN finance_metrics.{{resolve_table_name("s2h_ebe_financial_breakdown")}} AS fb ON d.datadate = fb.datadate
WHERE d.datadate = {{hive_date_format}} and fb.datadate = {{hive_date_format}}