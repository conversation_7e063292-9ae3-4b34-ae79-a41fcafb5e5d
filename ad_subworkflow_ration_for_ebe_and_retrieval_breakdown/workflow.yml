workflow:
  id: ad_subworkflow_ration_for_ebe_and_retrieval_breakdown
  name: ad_subworkflow_ration_for_ebe_and_retrieval_breakdown
  description: ''
  version: 2
actions:
  - id: s2h-ebe-financial-breakdown-ijig
    name: s2h_ebe_financial_breakdown
    type: s2h
    spec:
      query: s2h_ebe_financial_breakdown-ijig.query.sql
      targetTable: '{{resolve_table_name("s2h_ebe_financial_breakdown")}}'
      targetDatabase: finance_metrics
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
      sqlConnection:
        id: sg-dwsec_-_agoda_core_-_finance_platform_rw_user
    dependsOn:
      - fork-hcja
  - id: s2h-breakdown-retrieval-hdbe
    name: s2h_breakdown_retrieval
    type: s2h
    spec:
      query: s2h_breakdown_retrieval-hdbe.query.sql
      targetTable: '{{resolve_table_name("s2h_breakdown_retrieval")}}'
      targetDatabase: finance_metrics
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
      sqlConnection:
        id: sg-dwsec_-_agoda_finance_-_finance_platform_rw_user
    dependsOn:
      - fork-hcja
  - id: h2h-ratio-for-ebe-and-breakdown-retrieval-fjel
    name: h2h_ratio_for_ebe_and_breakdown_retrieval
    type: h2h
    spec:
      query: h2h_ratio_for_ebe_and_breakdown_retrieval-fjel.query.sql
      targetTable: '{{resolve_table_name("daily_ratio_of_ebe_and_retrival_breakdown")}}'
      targetDatabase: finance_metrics
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - join-heaf
  - id: fork-hcja
    name: fork-hcja
    type: fork
    dependsOn: []
  - id: join-heaf
    name: join-heaf
    type: join
    dependsOn:
      - s2h-ebe-financial-breakdown-ijig
      - s2h-breakdown-retrieval-hdbe
params_profile:
  - name: default
    params:
      - name: sql_from_date
        value: __deferred
        dataType: date
      - name: sql_to_date
        value: __deferred
        dataType: date
      - name: hive_date_format
        value: __deferred
        dataType: date
  - name: dev
    params: []
schedules: []
