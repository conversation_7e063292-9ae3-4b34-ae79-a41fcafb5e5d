workflow:
  id: daily-booking-cofunding-report-wf
  name: daily-booking-cofunding-report-wf
  description: ''
  version: 2
actions:
  - id: daily-cofunding-report-wf-dhdg
    name: Daily Cofunding Report WF
    type: sub-workflow
    spec:
      workflowPath: >-
        git://etl-workflows/wf-service-accounts/hk-fin-ar-prod-svc/wf_fin_report_all_view#main
      subWorkflowProperties:
        - key: as_of_date
          value: '{{as_of_date}}'
        - key: db_name
          value: '{{db_name}}'
        - key: destination_file_name
          value: '{{destination_file_name}}'
        - key: report_name
          value: '{{report_name}}'
        - key: publish_to_sftp
          value: '{{publish_to_sftp}}'
        - key: publish_to_vast
          value: '{{publish_to_vast}}'
        - key: report_format_name
          value: '{{report_format_name}}'
        - key: reporter_report_table
          value: '{{reporter_report_table}}'
        - key: sftp_path
          value: '{{sftp_path}}'
        - key: spark_queue
          value: '{{spark_queue}}'
        - key: src_finance_table
          value: '{{src_finance_table}}'
        - key: table_classification
          value: '{{table_classification}}'
        - key: vast_path
          value: '{{vast_path}}'
        - key: view_aggregation
          value: '{{view_aggregation}}'
        - key: view_classification
          value: '{{view_classification}}'
        - key: classification_val_exec_flow_id
          value: '{{classification_val_exec_flow_id}}'
        - key: report_table_val_exec_flow_id
          value: '{{report_table_val_exec_flow_id}}'
      propagateConfigurations: true
    dependsOn: []
params_profile:
  - name: default
    params:
      - name: as_of_date
        value: __deferred
        dataType: date
      - name: db_name
        value: finance_multiproduct_ar_reports
        dataType: string
      - name: destination_file_name
        value: DailyBooking_AR_Cofunding_ACS
        dataType: string
      - name: report_name
        value: cofunding_acs
        dataType: string
      - name: publish_to_sftp
        value: 'true'
        dataType: string
      - name: publish_to_vast
        value: 'true'
        dataType: string
      - name: report_format_name
        value: oracle
        dataType: string
      - name: reporter_report_table
        value: report_table
        dataType: string
      - name: sftp_path
        value: /upload/multi-product/ERPInbound_Live/ar/DailyBooking_AR_Cofunding
        dataType: string
      - name: spark_queue
        value: production
        dataType: string
      - name: src_finance_table
        value: finance_cofunding.valid_daily_transactions
        dataType: string
      - name: table_classification
        value: classified_table
        dataType: string
      - name: vast_path
        value: >-
          s3a://hk-fin-ar-prod-svc/reports/multi-product/erp/DailyBooking_AR_Cofunding
        dataType: string
      - name: view_aggregation
        value: aggregated_view
        dataType: string
      - name: view_classification
        value: classified_view
        dataType: string
      - name: classification_val_exec_flow_id
        value: '103293'
        dataType: string
      - name: report_table_val_exec_flow_id
        value: '103294'
        dataType: string
  - name: dev
    params:
      - name: db_name
        value: finance_multiproduct_ar_reports_dev
        dataType: string
      - name: sftp_path
        value: /upload/multi-product/ERPInbound_QA/ar/DailyBooking_AR_Cofunding
        dataType: string
      - name: vast_path
        value: >-
          s3a://hk-fin-ar-prod-svc/reports/multi-product/erp/uat/DailyBooking_AR_Cofunding
        dataType: string
      - name: reporter_report_table
        value: report_table_dev
        dataType: string
      - name: view_classification
        value: classified_view_dev
        dataType: string
      - name: view_aggregation
        value: aggregated_view_dev
        dataType: string
      - name: table_classification
        value: classified_table_dev
        dataType: string
      - name: spark_queue
        value: default
        dataType: string
schedules: []
