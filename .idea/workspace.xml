<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="8081c10a-9f7b-4513-8ae6-7638a0aee97f" name="Changes" comment="fix" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitToolBoxStore">
    <option name="projectConfigVersion" value="5" />
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="main" />
                    <option name="lastUsedInstant" value="1716202565" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="feature/b2b-ar-reconciliator/MH-7894" />
                    <option name="lastUsedInstant" value="1715758679" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="ProjectCodeStyleSettingsMigration">
    <option name="version" value="2" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2eAA8qjTcPQZktvFnDiNGhFw9xU" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "git-widget-placeholder": "feature/insert-uninvoiced-with-keeping-until-invoice-date/initial",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "/Users/<USER>/Documents/Agoda/gitlab/hk-fin-ar-prod-svc",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="hk-fin-ar-prod-svc" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="hk-fin-ar-prod-svc" />
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.17890.1" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.17890.1" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="8081c10a-9f7b-4513-8ae6-7638a0aee97f" name="Changes" comment="" />
      <created>1711336852873</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1711336852873</updated>
      <workItem from="1711336853904" duration="2546000" />
      <workItem from="1711436557413" duration="599000" />
      <workItem from="1711667110480" duration="796000" />
      <workItem from="1711936209188" duration="1198000" />
      <workItem from="1712057260821" duration="510000" />
      <workItem from="1712057787796" duration="599000" />
      <workItem from="1712074470252" duration="69000" />
      <workItem from="1715758555762" duration="1870000" />
      <workItem from="1715766309611" duration="1813000" />
      <workItem from="1716117958206" duration="557000" />
      <workItem from="1716202549553" duration="2716000" />
      <workItem from="1728613933165" duration="221000" />
    </task>
    <task id="LOCAL-00001" summary="merge: merge from main">
      <option name="closed" value="true" />
      <created>1715758904416</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1715758904416</updated>
    </task>
    <task id="LOCAL-00002" summary="fix">
      <option name="closed" value="true" />
      <created>1750753588205</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1750753588205</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="merge: merge from main" />
    <MESSAGE value="fix" />
    <option name="LAST_COMMIT_MESSAGE" value="fix" />
  </component>
</project>