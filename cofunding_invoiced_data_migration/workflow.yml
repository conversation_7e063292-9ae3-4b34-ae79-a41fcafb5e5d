workflow:
  id: cofunding_invoiced_data_migration
  name: cofunding_invoiced_data_migration
  description: >-
    Migrate data into invoiced_transactions_by_invoice_date table by combining
    data from migrated_invoiced_raw table and other finance tables
  version: 2
actions:
  - id: load-data-dklb
    name: load_data
    type: h2h
    spec:
      query: load_data-dklb.query.sql
      targetTable: '{{table_name}}'
      targetDatabase: '{{db_name}}'
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        append: {}
    dependsOn: []
params_profile:
  - name: default
    params:
      - name: db_name
        value: finance_cofunding
        dataType: string
      - name: table_name
        value: invoiced_transactions_by_invoice_date
        dataType: string
      - name: datadate
        value: __deferred
        dataType: number
  - name: dev
    params:
      - name: db_name
        value: finance_cofunding_uat
        dataType: string
schedules: []
