SELECT
CAST(0 as int) AS invoice_id,
CAST(42 as int) AS invoice_method_id, 
CAST('OLD_MIGRATED_DATA' as string) AS reference_number, 
CAST(MIR.paying_aid_1 as int) AS payer_source_id, 
CAST('2025-03-31 00:00:00.000' as timestamp) AS invoice_date, 
CAST('2024-01-01 00:00:00.000' as timestamp) AS period_from, 
CAST('2025-03-31 00:00:00.000' as timestamp) AS period_to, 
CAST('USD' as string) AS currency_code, 
CAST(0 as double) as header_amount,
CAST(0 as double) as net_amount,
CAST(0 as int) AS invoice_status_id, 
CAST(0 as int) AS document_id, 
CAST(1 as int) AS rec_status, 
CAST(current_timestamp() as timestamp) AS rec_created_when, 
CAST('' as string) AS rec_created_by, 
CAST(current_timestamp() as timestamp) AS rec_modified_when, 
CAST('' as string) AS rec_modify_by, 
CAST(0 as int) AS affiliate_site_id, 
CAST(0 as int) AS credit_term, 
CAST(0 as double) as total_gst_amount,
CAST('' as string) AS functional_currency, 
CAST('' as string) AS partner_name, 
CAST('' as string) AS partner_address, 
CAST(0 as double) as gst_rate,
CAST('' as string) AS criteria_date, 
CAST('' as string) AS partner_email, 
CAST(MIR.campaign_start_date as timestamp) AS campaign_start, 
CAST(MIR.discount_amount as double) as discount_rate,
CAST('' as string) AS title, 
CAST('' as string) AS invoice_currency, 
CAST(MIR.campaign_end_date as timestamp) AS campaign_end, 
CAST(0 as int) AS invoice_transaction_id, 
CAST(MIR.booking_id as bigint) AS booking_id, 
CAST(MIR.booking_date as timestamp) AS booking_date, 
CAST(MIR.check_in_date as timestamp) AS checkin_date, 
CAST(MIR.check_out_date as timestamp) AS checkout_date, 
CAST(MIR.partner_1_total_funding as double) as transaction_amount,
CAST(0 as int) AS hotel_id, 
CAST(0 as double) as booking_amount,
CAST('USD' as string) AS cofunding_currency, 
CAST('' as string) AS invoiced_currency, 
CAST(MIR.partner_1_total_funding as double) as invoice_amount,
CAST(MIR.partner_1_total_funding as double) as cofunding_amount,
CAST(0 as double) as gst_amount_usd,
CAST(MIR.campaign_id as int) AS promotion_campaign_id, 
CAST('' as string) as description,
CAST(0 as double) as sub_total_partner_funding_amount_SGD,
CAST(0 as double) as total_partner_funding_amount_SGD,
CAST(0 as double) as exchange_rate_used,
CAST(0 as double) as sub_total_partner_funding_amount_USD,
CAST(0 as double) as tax_partner_funding_amount_SGD,
CAST(0 as double) as total_partner_funding_amount_USD,
CAST(0 as double) as total_partner_funding_amount_invoice_currency,
CAST({{datadate}} as int) AS datadate
FROM finance_cofunding.migrated_invoiced_raw MIR
WHERE discount_type = 'Discount Postpaid'