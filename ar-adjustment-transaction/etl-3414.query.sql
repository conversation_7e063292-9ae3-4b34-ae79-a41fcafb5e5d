SELECT 
                ar_adjustment_file_details_id
                ,uuid
		,transaction_id
		,booking_id
		,reference_type
		,local_currency
		,cast(local_amount as decimal(18,8)) as local_amount 
		,functional_currency
                ,cast(functional_amount  as decimal(18,8)) as functional_amount 
		,reason_id
		,accounting_date
		,gateway
		,revenue_entity
		,ar_adjustment_file_id
		,datadate 
FROM finance_multiproduct_staging.adjustment_transactions
WHERE datadate  = '{{hive_datadate_from}}' 