workflow:
  id: ar-adjustment-transaction
  name: AR_Adjustment_Transaction
  description: >-
    Owner: <EMAIL>; Purpose: Fetch AR Adjustment Transaction;
    Coordinator_names: AR_Reconciliation_and_Reproting; is_temporary: No;
    Created by: <EMAIL>; Updated by:
    <EMAIL>;
  version: 2
params_profile:
  - name: default
    params:
      - name: sql_event_date_to
        value: __deferred
        dataType: date
      - name: sql_event_date_from
        value: __deferred
        dataType: date
      - name: hive_datadate_from
        value: __deferred
        dataType: date
  - name: dev
    params: []
actions:
  - id: etl-0b6c
    name: etl-0b6c
    type: eval
    spec:
      sqlConnection:
        id: sg-dwsec_-_application_finance_-_finance_platform_rw_user
      query: etl-0b6c.query.sql
    dependsOn: []
    maxRetry: 3
    retryInterval: 10
  - id: etl-5f9a
    name: etl-5f9a
    type: s2h
    spec:
      query: etl-5f9a.query.sql
      targetTable: adjustment_transactions
      targetDatabase: finance_multiproduct_staging
      quilliup:
        enableQuilliup: false
      mergeBy:
        overwritePartition: {}
      sparkSetting:
        compressionCodecIs: snappy
        enableCheckpoint: false
      additionalSparkConfigurations: []
      enableValidationRule: false
      computeStatsEnabled: false
      alterTableSchema: true
      asStringColumns:
        - accounting_date
      loggingTimesEnabled: false
      partitionFolderTemplate: '{partition_value}'
      deleteOldVersionsOlderThan: 192
      keepOldVersionsNewerThan: 8
      dataOutputFormat: parquet
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      distributeBy:
        auto: {}
      sqlConnection:
        id: sg-dwsec_-_agoda_finance_-_finance_platform_rw_user
        numberOfConnections: 1
    maxRetry: 3
    retryInterval: 10
    dependsOn:
      - etl-0b6c
  - id: etl-3414
    name: etl-3414
    type: h2h
    spec:
      query: etl-3414.query.sql
      targetTable: ar_b2c_adjustment_transactions
      targetDatabase: finance_multiproduct
      quilliup:
        enableQuilliup: false
      mergeBy:
        overwritePartition: {}
      sparkSetting:
        compressionCodecIs: snappy
        enableCheckpoint: false
      additionalSparkConfigurations:
        - key: spark.sql.parquet.writeLegacyFormat
          value: 'true'
      enableValidationRule: false
      computeStatsEnabled: false
      alterTableSchema: true
      loggingTimesEnabled: false
      partitionFolderTemplate: '{partition_value}'
      deleteOldVersionsOlderThan: 192
      keepOldVersionsNewerThan: 8
      dataOutputFormat: parquet
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      distributeBy:
        auto: {}
    maxRetry: 3
    retryInterval: 10
    dependsOn:
      - etl-5f9a
schedules:
  - id: mp-ar-adjustment-transaction
    name: MP_AR_Adjustment_Transaction
    timezone: Asia/Bangkok
    cron: 0 18 * * *
    execution: FIFO
    parameters:
      - name: sql_event_date_to
        value:
          dateFormat: yyyy-MM-dd
          offsets:
            - offset: 1
              format: Day
        dataType: date
      - name: sql_event_date_from
        value:
          dateFormat: yyyy-MM-dd
          offsets:
            - offset: 0
              format: Hour
        dataType: date
      - name: hive_datadate_from
        value:
          dateFormat: yyyyMMdd
          offsets:
            - offset: 0
              format: Day
        dataType: date
