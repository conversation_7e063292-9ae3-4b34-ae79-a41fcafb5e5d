workflow:
  id: bank-statement-monthly-report-wf
  name: bank-statement-monthly-report-wf
  description: ''
  version: 2
actions:
  - id: bank-statment-downloader-dkig
    name: bank-statment-downloader
    type: sub-workflow
    spec:
      workflowPath: >-
        git://etl-workflows/wf-service-accounts/hk-fin-ar-prod-svc/downloader-wf#main
      subWorkflowProperties:
        - key: config
          value: '{{downloader_config}}'
        - key: tag
          value: '{{downloader_tag}}'
        - key: appArg
          value: '{{downloader_appArg}}'
      propagateConfigurations: true
    dependsOn: []
  - id: bank-statement-normalizer-ffak
    name: bank-statement-normalizer
    type: sub-workflow
    spec:
      workflowPath: >-
        git://etl-workflows/wf-service-accounts/hk-fin-ar-prod-svc/normalizer-wf#main
      subWorkflowProperties:
        - key: config
          value: '{{normalizer_config}}'
        - key: tag
          value: '{{normalizer_tag}}'
        - key: appArg
          value: '{{normalizer_appArg}}'
      propagateConfigurations: true
    dependsOn:
      - bank-statment-downloader-dkig
  - id: bank-statement-reporter-hchd
    name: bank-statement-reporter
    type: sub-workflow
    spec:
      workflowPath: >-
        git://etl-workflows/wf-service-accounts/hk-fin-ar-prod-svc/treasury-reporter#main
      subWorkflowProperties:
        - key: datadate
          value: '{{datadate}}'
        - key: reporter_args
          value: '{{reporter_args}}'
        - key: tag
          value: '{{reporter_tag}}'
        - key: config
          value: '{{reporter_config}}'
        - key: log4j_config
          value: '{{reporter_log4j_config}}'
        - key: submit_user
          value: '{{submit_user}}'
      propagateConfigurations: true
    dependsOn:
      - bank-statement-normalizer-ffak
params_profile:
  - name: default
    params:
      - name: downloader_config
        value: instances/bank_statement_file/prod.conf
        dataType: string
      - name: downloader_tag
        value: release
        dataType: string
      - name: downloader_appArg
        value: mode=prod
        dataType: string
      - name: normalizer_config
        value: bank_statement_normalized_data/prod.conf
        dataType: string
      - name: normalizer_tag
        value: release
        dataType: string
      - name: normalizer_appArg
        value: mode=prod
        dataType: string
      - name: datadate
        value: __deferred
        dataType: date
      - name: reporter_args
        value: slack=false
        dataType: string
      - name: reporter_tag
        value: release
        dataType: string
      - name: reporter_config
        value: sg/release_treasury.conf
        dataType: string
      - name: reporter_log4j_config
        value: log4j_adp.properties
        dataType: string
      - name: submit_user
        value: hk-fin-ar-prod-svc
        dataType: string
  - name: dev
    params:
      - name: downloader_config
        value: instances/bank_statement_file/dev.conf
        dataType: string
      - name: normalizer_config
        value: bank_statement_normalized_data/dev.conf
        dataType: string
      - name: reporter_config
        value: sg/dev_treasury.conf
        dataType: string
      - name: reporter_log4j_config
        value: log/log4j_adp_qa.properties
        dataType: string
      - name: submit_user
        value: hk-fin-ar-prod-svc--dev
        dataType: string
schedules:
  - id: monthly-schedule-ggdb
    name: monthly-schedule
    timezone: UTC
    cron: 0 7 1-5 * *
    parameters:
      - dataType: date
        name: datadate
        value:
          dateFormat: yyyyMMdd
          offsets: []
    execution: FIFO
