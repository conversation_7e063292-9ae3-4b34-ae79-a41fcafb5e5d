workflow:
  id: backup_invoiced_cofunding_20250516
  name: backup_invoiced_cofunding_20250516
  description: ''
  version: 2
actions:
  - id: backup20250516-cjfa
    name: backup20250516
    type: h2h
    spec:
      query: backup20250516-cjfa.query.sql
      targetTable: invoiced_transactions_by_invoice_date_backup_20250516
      targetDatabase: finance_cofunding
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: source
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn: []
params_profile:
  - name: default
    params: []
  - name: dev
    params: []
schedules: []
