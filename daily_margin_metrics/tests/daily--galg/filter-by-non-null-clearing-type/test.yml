description: Verify that records with null clearing_type_name are excluded from the results
queryParams:
  - name: hive_datadate
    value: '********'
    dataType: date
  - name: from_sql_datadate
    value: '********'
    dataType: date
  - name: to_sql_datadate
    value: '********'
    dataType: date
  - name: from_accounting_date
    value: '2025-03-06'
    dataType: date
  - name: to_accounting_date
    value: '2025-03-07'
    dataType: date
  - name: hive_yesterday_datadate
    value: '********'
    dataType: date
strictColumnCheck: false
