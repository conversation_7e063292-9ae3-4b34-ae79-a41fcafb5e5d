workflow:
  id: b2b-invoice-monitoring
  name: B2B_Invoice_Monitoring
  description: ''
  version: 2
params_profile:
  - name: default
    params:
      - name: invoice_date
        value: __deferred
        dataType: string
  - name: dev
    params: []
actions:
  - id: shell-7287
    name: shell-7287
    type: shell
    spec:
      commandFile: hdfs:///user/hk-fin-ar-prod-svc/b2b_invoice_monitoring/call_mbh.sh
      arguments:
        - '{{invoice_date}}'
      captureOutput: true
      files:
        - hdfs:///user/hk-fin-ar-prod-svc/b2b_invoice_monitoring/call_mbh.sh
    dependsOn: []
schedules:
  - id: b2b_inv-b079
    name: B2B_Invoice_Monitoring_Scheduler
    timezone: UTC
    cron: 0 5 1,16 * 2
    parameters:
      - dataType: string
        name: invoice_date
        value: >-
          ${coord:formatTime(coord:dateOffset(coord:nominalTime(), 0, 'HOUR'),
          'yyyy-MM-dd')}
