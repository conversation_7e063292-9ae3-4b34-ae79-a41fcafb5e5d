SELECT
    scr.product_type AS product_type,

    -- Booking and Amount Aggregations
    COUNT(DISTINCT scr.booking_id) AS all_booking_count,
    SUM(COALESCE(scr.cost, 0)) AS all_cost_amount,
    SUM(COALESCE(scr.sale, 0)) AS all_sale_amount,

    -- Cost-only and Sale-only Bookings
    COUNT(DISTINCT CASE WHEN scr.sale > 0 AND scr.cost = 0 THEN scr.booking_id END) AS is_sale_only_booking_count,

    -- Cost-only and Sale-only Amounts
    SUM(CASE WHEN scr.sale > 0 AND scr.cost = 0 THEN scr.sale ELSE 0 END) AS is_sale_only_amount,

    -- Ratios (Bookings)
    COUNT(DISTINCT CASE WHEN scr.sale > 0 AND scr.cost = 0 THEN scr.booking_id END) * 1.0 /
        NULLIF(COUNT(DISTINCT scr.booking_id), 0) AS is_sale_only_vs_all_booking_count_ratio,

    -- Ratios (Amounts)
    SUM(CASE WHEN scr.sale > 0 AND scr.cost = 0 THEN scr.sale ELSE 0 END) * 1.0 /
        NULLIF(SUM(COALESCE(scr.sale, 0)), 0) AS is_sale_only_vs_all_amount_ratio,

    -- Date Stamp
    {{hive_yesterday_datadate}} AS datadate

FROM (
    SELECT
        booking_id,
        product_type,
        SUM(CASE WHEN source = 'SALE' THEN usd_defer_amount ELSE 0 END) AS sale,
        SUM(CASE WHEN source = 'COST' THEN usd_amount_exc_gst ELSE 0 END) AS cost,
        MIN(start_date) AS start_date
    FROM finance_multiproduct_scr_daily.sale_cost_recognition
    WHERE booking_id IN (
        SELECT booking_id
        FROM finance_multiproduct_scr_daily.sale_cost_recognition
        WHERE datadate = {{hive_yesterday_datadate}}
            AND status = 'RECOGNIZED'
            AND source = 'SALE'
            AND booking_id IS NOT NULL
    )
        AND status = 'RECOGNIZED'
    GROUP BY booking_id, product_type
) scr
LEFT JOIN finance_dw.booking_info bi
    ON scr.booking_id = bi.booking_id
    AND scr.product_type = bi.product_type

GROUP BY scr.product_type

HAVING
    COUNT(DISTINCT scr.booking_id) > 0
    AND SUM(COALESCE(scr.sale, 0)) > 0

ORDER BY scr.product_type;