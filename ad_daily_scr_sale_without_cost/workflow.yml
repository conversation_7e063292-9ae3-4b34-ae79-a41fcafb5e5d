workflow:
  id: ad_daily_scr_sale_without_cost
  name: ad_daily_scr_sale_without_cost
  description: ''
  version: 2
actions:
  - id: write-metric-babc
    name: write_metric
    type: h2h
    spec:
      query: write-metric-babc.query.sql
      targetTable: '{{resolve_table_name("daily_scr_sale_without_cost")}}'
      targetDatabase: finance_metrics
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: source
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn: []
params_profile:
  - name: default
    params:
      - name: hive_yesterday_datadate
        value: __deferred
        dataType: date
  - name: dev
    params: []
schedules: []
