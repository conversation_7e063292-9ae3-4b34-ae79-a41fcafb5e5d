workflow:
  id: cofunding_uninvoice_draft_table
  name: cofunding_uninvoice_draft_table
  description: ''
  version: 2
actions:
  - id: uninvoiced-transactions-kbal
    name: uninvoiced_transactions
    type: h2h
    spec:
      query: uninvoiced_transactions-kbal.query.sql
      targetTable: uninvoiced_transactions
      targetDatabase: '{{finance_cofunding_db}}'
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn: []
params_profile:
  - name: default
    params:
      - name: finance_cofunding_db
        value: finance_cofunding
        dataType: string
  - name: dev
    params:
      - name: finance_cofunding_db
        value: finance_cofunding_uat
        dataType: string
schedules: []
