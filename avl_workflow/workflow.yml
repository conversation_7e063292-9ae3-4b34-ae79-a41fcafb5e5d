workflow:
  id: avl_workflow
  name: avl_workflow
  description: >-
    Owner: <EMAIL>; Purpose: workflow to trigger avl bank
    statement and avl finance reporter; is_temporary: No; Created by:
    <EMAIL>; Updated by: <EMAIL>;
  version: 2
actions:
  - id: a--1asdf-fjki
    name: avl_bank_statement
    type: sub-workflow
    spec:
      workflowPath: >-
        git://etl-workflows/wf-service-accounts/hk-fin-ar-prod-svc/avl_bank_statement#main
      subWorkflowProperties:
        - key: datadate
          value: '{{datadate}}'
      propagateConfigurations: true
    dependsOn: []
  - id: finance-reporter-avl-hcic
    name: finance_reporter_avl
    type: sub-workflow
    spec:
      workflowPath: >-
        git://etl-workflows/wf-service-accounts/hk-fin-ar-prod-svc/finance_reporter_avl#main
      subWorkflowProperties:
        - key: subworkflow_path
          value: avl
        - key: env
          value: production
        - key: datadate
          value: '{{datadate}}'
        - key: args
          value: '{{appArg}}'
        - key: submitUser
          value: hk-fin-ar-prod-svc
        - key: configResource
          value: sg/prod_avl.conf
      propagateConfigurations: true
    dependsOn:
      - a--1asdf-fjki
  - id: finance-reporter-avl-email-fdja
    name: finance_reporter_avl_email
    type: email
    spec:
      to:
        - <EMAIL>
      cc: []
      bcc: []
      subject: Successfully Today's AVL Report was Generated
      attachments: []
      contentType: text/plain
      bodyFile: finance_reporter_avl_email-fdja.body.txt
    dependsOn:
      - finance-reporter-avl-hcic
params_profile:
  - name: default
    params:
      - name: datadate
        value: __deferred
        dataType: string
      - name: appArg
        value: __deferred
        dataType: string
  - name: dev
    params: []
schedules: []
