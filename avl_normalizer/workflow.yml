workflow:
  id: avl_normalizer
  name: avl_normalizer
  description: ''
  version: 2
actions:
  - id: avl-normalizer-edbg
    maxRetry: 0
    name: avl_normalizer
    spec:
      mainApplicationFile: local:///opt/app/assembly/assembly.jar
      image: reg-sg.agodadev.io/gitlab/financeplatform/reconciliation-platform/external-data-pipeline/normalizer
      tag: '{{tag}}'
      additionalSparkConfigurations:
        - key: spark.serializer
          value: org.apache.spark.serializer.KryoSerializer
        - key: spark.hadoop.hadoop.security.credential.provider.path
          value: ''
        - key: spark.kerberos.access.hadoopFileSystems
          value: hdfs://nameservice1,hdfs://nameservice2
        - key: spark.sql.autoBroadcastJoinThreshold
          value: 10M
        - key: spark.eventLog.enabled
          value: 'true'
        - key: spark.executor.memoryOverhead
          value: '16384'
        - key: spark.sql.adaptive.enabled
          value: 'true'
        - key: spark.yarn.populateHadoopClasspath
          value: 'true'
        - key: spark.hadoop.smartloader.newtable-vast
          value: 'true'
        - key: spark.driver.maxResultSize
          value: 7g
        - key: spark.queue
          value: production
        - key: spark.shuffle.service.enabled
          value: 'true'
        - key: spark.sql.hive.metastore.token.signature
          value: hiveserver2ClientToken
        - key: spark.yarn.queue
          value: production
        - key: spark.kryoserializer.buffer.max
          value: 256m
        - key: spark.sql.parquet.int96RebaseModeInRead
          value: LEGACY
        - key: spark.hadoop.hive.execution.engine
          value: mr
        - key: spark.sql.hive.metastore.jars
          value: /usr/hdp/current/hive-client/lib/*:./*
        - key: spark.storage.memoryFraction
          value: '0.2'
        - key: spark.driver.memory
          value: 20g
        - key: spark.executor.instances
          value: '50'
        - key: spark.storage.memoryMapThreshold
          value: '2097152'
        - key: spark.logConf
          value: 'true'
        - key: spark.driver.memoryOverhead
          value: '16384'
        - key: spark.sql.sources.partitionOverwriteMode
          value: DYNAMIC
        - key: spark.yarn.maxAppAttempts
          value: '1'
        - key: spark.executor.memory
          value: 23g
        - key: spark.executor.cores
          value: '3'
        - key: spark.sql.parquet.writeLegacyFormat
          value: 'true'
        - key: spark.sql.parquet.int96RebaseModeInWrite
          value: LEGACY
        - key: hive.metastore.disallow.incompatible.col.type.changes
          value: 'false'
        - key: spark.sql.hive.metastore.version
          value: 3.1.1
      sparkVersion: 3.3.1
      mainClass: com.agoda.finance.Main
      driver:
        memoryOverhead: 16384m
        javaOptions:
          - '-XX:+CMSClassUnloadingEnabled'
          - '-XX:+HeapDumpOnOutOfMemoryError'
          - '-XX:HeapDumpPath=/agoda/heap_dump/'
          - '-Dhadoop.postgres.connectionString=********************************************************'
          - '-Detl.services.refresh.server-location=PROD_SGP'
          - '-Dhive.metastore.filter.hook=com.agoda.adp.hadoop.hive.client.hook.AdpFilterHook'
          - '-Dconfig.strategy=com.agoda.ml.config.MultiResourceStrategy'
          - '-Dconfig.resource={{normalizer_configResource}}'
          - '-Dlog4j.configuration=log4j_adp.properties'
          - '-Dhadoop.spark.submituser=hk-fin-ar-prod-svc'
        memory: 20g
      dynamicAllocation:
        maxExecutors: 20
        minExecutors: 0
        initialExecutors: 1
      executor:
        instances: '50'
        cores: '3'
        memory: 23g
        memoryOverhead: 16384m
        javaOptions:
          - '-XX:+CMSClassUnloadingEnabled'
          - '-XX:+HeapDumpOnOutOfMemoryError'
          - '-XX:HeapDumpPath=/agoda/heap_dump/'
          - '-Dhadoop.postgres.connectionString=********************************************************'
          - '-Detl.services.refresh.server-location=PROD_SGP'
          - '-Dconfig.strategy=com.agoda.ml.config.MultiResourceStrategy'
          - '-Dconfig.resource={{normalizer_configResource}}'
          - '-Dlog4j.configuration=log4j_adp.properties'
          - '-Dhadoop.spark.submituser=hk-fin-ar-prod-svc'
      dependencies:
        jars:
          - local:///opt/app/assembly/dependency_cache.jar
        files:
          - local:///opt/app/deps/files/log4j_adp.properties
      arguments:
        - 'mode=reprocess processdate={{datadate}} partitiondate={{datadate}} mail={{email}}'
    type: spark
    retryInterval: 10
    dependsOn: []
params_profile:
  - name: default
    params:
      - name: tag
        value: release
        dataType: string
      - name: datadate
        value: __deferred
        dataType: string
      - name: normalizer_configResource
        value: __deferred
        dataType: string
      - name: email
        value: __deferred
        dataType: string
  - name: dev
    params: []
schedules: []
