workflow:
  id: backfilled-b2b-ar-reconciliator
  name: Backfilled-b2b-ar-reconciliator
  description: ''
  version: 2
actions:
  - id: backfilled-2d06
    name: backfilled
    type: sub-workflow
    spec:
      workflowPath: >-
        git://etl-workflows/wf-service-accounts/hk-fin-ar-prod-svc/b2b-ar-reconciliator#main
      subWorkflowProperties: []
      propagateConfigurations: true
    dependsOn: []
params_profile:
  - name: default
    params:
      - name: hive_today_date
        value: >-
          ${coord:formatTime(coord:dateOffset(coord:nominalTime(), 0, 'DAY'),
          'yyyyMMdd')}
        dataType: string
      - name: from
        value: >-
          ${coord:formatTime(coord:dateOffset(coord:nominalTime(), -3, 'DAY'),
          'yyyy-MM-dd 16:00:00')}
        dataType: string
      - name: to
        value: >-
          ${coord:formatTime(coord:dateOffset(coord:nominalTime(), 0, 'DAY'),
          'yyyy-MM-dd 16:00:00')}
        dataType: string
      - name: finance_b2b_db
        value: finance_b2b
        dataType: string
      - name: env
        value: production
        dataType: string
      - name: ar_recon_config
        value: sg/b2b/release.conf
        dataType: string
      - name: reporter_args
        value: slack=true
        dataType: string
      - name: reporter_multiproduct_daily_config
        value: sg/b2b/release_multiproduct_daily.conf
        dataType: string
  - name: dev
    params: []
schedules: []
