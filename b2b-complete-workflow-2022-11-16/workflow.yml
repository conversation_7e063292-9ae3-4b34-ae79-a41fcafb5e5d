workflow:
  id: b2b-complete-workflow-2022-11-16
  name: b2b-invoice-processor-and-daily-booking-report
  description: >-
    Owner: <EMAIL>; Purpose: Complete daily workflow for
    b2b uninvoiced flow and daily booking flow; Coordinator_names: B2B Daily
    UnInvoice and Daily Booking; is_temporary: No; Created by:
    a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@agoda.com; Updated by: <EMAIL>;
  version: 2
params_profile:
  - name: default
    params:
      - name: hive_today_date
        value: __deferred
        dataType: date
      - name: hive_yesterday_date
        value: __deferred
        dataType: date
      - name: finance_multiproduct_db
        value: finance_multiproduct
        dataType: string
      - name: finance_b2b_db
        value: finance_b2b
        dataType: string
      - name: reporter_b2b_daily_booking_config
        value: sg/b2b/release_daily_booking.conf
        dataType: string
      - name: reporter_args
        value: slack=true
        dataType: string
      - name: b2b_processor_config
        value: release.conf
        dataType: string
      - name: email_recipients
        value: <EMAIL>
        dataType: string
      - name: is_upload_to_erp
        value: 'true'
        dataType: string
      - name: erp_connector_server
        value: prod
        dataType: string
      - name: report_upload_date
        value: __deferred
        dataType: date
      - name: invoice_processor_path
        value: production
        dataType: string
      - name: env
        value: production
        dataType: string
      - name: submitUser
        value: hk-fin-ar-prod-svc
        dataType: string
      - name: env_path
        value: release
        dataType: string
      - name: subworkflow_path
        value: b2b_daily_booking
        dataType: string
      - name: queue_name
        value: production
        dataType: string
      - name: log4j_config
        value: log4j_adp.properties
        dataType: string
      - name: reporter_b2b_invoice_config
        value: sg/b2binvoice/prod_b2b_invoice.conf
        dataType: string
      - name: gen_b2b_invoice_report
        value: '1'
        dataType: string
      - name: reporter_b2b_discount_margin_config
        value: sg/b2binvoice/prod_b2b_discount_margin.conf
        dataType: string
      - name: gen_b2b_discount_margin
        value: '1'
        dataType: string
      - name: day_of_week
        value: __deferred
        dataType: date
      - name: report_upload_date_minus_one_day
        value: __deferred
        dataType: date
  - name: dev
    params:
      - name: finance_multiproduct_db
        value: finance_multiproduct_dev
        dataType: string
      - name: finance_b2b_db
        value: finance_b2b_dev
        dataType: string
      - name: reporter_b2b_daily_booking_config
        value: sg/b2b/dev_daily_booking.conf
        dataType: string
      - name: b2b_processor_config
        value: dev.conf
        dataType: string
      - name: reporter_args
        value: slack=false
        dataType: string
      - name: email_recipients
        value: <EMAIL>
        dataType: string
      - name: erp_connector_server
        value: dev
        dataType: string
      - name: invoice_processor_path
        value: dev/master
        dataType: string
      - name: env
        value: dev
        dataType: string
      - name: submitUser
        value: hk-fin-ar-prod-svc--dev
        dataType: string
      - name: env_path
        value: master
        dataType: string
      - name: queue_name
        value: default
        dataType: string
      - name: log4j_config
        value: log/log4j_adp_qa.properties
        dataType: string
      - name: reporter_b2b_invoice_config
        value: sg/b2binvoice/staging_b2b_invoice.conf
        dataType: string
      - name: reporter_b2b_discount_margin_config
        value: sg/b2binvoice/staging_b2b_discount_margin.conf
        dataType: string
actions:
  - id: subworkflow-343e
    name: b2b-processor
    type: sub-workflow
    spec:
      workflowPath: >-
        git://etl-workflows/wf-service-accounts/hk-fin-ar-prod-svc/b2b-processor-daily-workflow-byworkflow-xml#main
      subWorkflowProperties:
        - key: invoice_processor_path
          value: '{{invoice_processor_path}}'
        - key: configResource
          value: '{{b2b_processor_config}}'
        - key: submitUser
          value: '{{submitUser}}'
        - key: hive_today_date
          value: '{{hive_today_date}}'
        - key: tag
          value: '{{env_path}}'
        - key: env
          value: '{{env}}'
      propagateConfigurations: true
    dependsOn:
      - join-dahg
  - id: subworkflow-baeb
    name: finance-reporter
    type: sub-workflow
    spec:
      workflowPath: >-
        git://etl-workflows/wf-service-accounts/hk-fin-ar-prod-svc/ar-reporter#main
      subWorkflowProperties:
        - key: datadate
          value: '{{hive_today_date}}'
        - key: reporter_args
          value: '{{reporter_args}}'
        - key: tag
          value: '{{env_path}}'
        - key: config
          value: '{{reporter_b2b_daily_booking_config}}'
        - key: log4j_config
          value: '{{log4j_config}}'
        - key: submit_user
          value: '{{submitUser}}'
      propagateConfigurations: true
    dependsOn:
      - fork-c82d
  - id: subworkflow-76f4
    name: etl-invoice-tax
    type: sub-workflow
    spec:
      workflowPath: >-
        git://etl-workflows/wf-service-accounts/hk-fin-ar-prod-svc/etl-invoice-tax#main
      subWorkflowProperties:
        - key: finance_b2b_db
          value: '{{finance_b2b_db}}'
        - key: queueName
          value: '{{queue_name}}'
      propagateConfigurations: true
    dependsOn:
      - fork-kebd
  - id: subworkflow-7ae8
    name: uninvoiced-casper
    type: sub-workflow
    spec:
      workflowPath: >-
        git://etl-workflows/wf-service-accounts/hk-fin-ar-prod-svc/uninvoiced-transactions-daily-h2h-for-casper#main
      subWorkflowProperties:
        - key: hive_today_date
          value: '{{hive_today_date}}'
        - key: finance_b2b_db
          value: '{{finance_b2b_db}}'
        - key: email_recipients
          value: '{{email_recipients}}'
        - key: queueName
          value: '{{queue_name}}'
      propagateConfigurations: true
    dependsOn:
      - fork-c82d
  - id: fork-c82d
    name: fork-c82d
    type: fork
    dependsOn:
      - subworkflow-343e
  - id: join-05d1
    name: join-05d1
    type: join
    dependsOn:
      - subworkflow-7ae8
      - upload-b2b-dailybooking-3b7f
      - upload-b2b-invoice-report-dgbj
      - upload-b2b-discount-margin-report-gike
  - id: upload-b2b-dailybooking-3b7f
    name: upload-b2b-dailybooking
    type: shell
    spec:
      commandFile: hdfs:///user/hk-fin-ar-prod-svc/shell_script/erpUpload.sh
      captureOutput: false
      files:
        - hdfs:///user/hk-fin-ar-prod-svc/shell_script/erpUpload.sh
      arguments:
        - '{{is_upload_to_erp}}'
        - '{{erp_connector_server}}'
        - '{{report_upload_date}}'
        - '128'
        - '129'
    dependsOn:
      - subworkflow-baeb
  - id: b2b-invoice-lajf
    name: b2b-invoice-reporter
    type: sub-workflow
    spec:
      workflowPath: >-
        git://etl-workflows/wf-service-accounts/hk-fin-ar-prod-svc/ar-reporter#main
      subWorkflowProperties:
        - key: datadate
          value: '{{hive_today_date}}'
        - key: reporter_args
          value: '{{reporter_args}}'
        - key: tag
          value: '{{env_path}}'
        - key: config
          value: '{{reporter_b2b_invoice_config}}'
        - key: log4j_config
          value: '{{log4j_config}}'
        - key: submit_user
          value: '{{submitUser}}'
      propagateConfigurations: true
    dependsOn:
      - gen-b2b-invoice-report
  - id: gen-b2b-invoice-report
    name: gen-b2b-invoice-report
    type: decision
    spec:
      cases:
        - condition: default
          goTo: join-05d1
        - condition: >-
            ${gen_b2b_invoice_report == 1 and (replaceAll(hive_today_date,
            '^[0-9]{4}([0-9]{2})([0-9]{2})$', '$2') == 17 or
            replaceAll(hive_today_date, '^[0-9]{4}([0-9]{2})([0-9]{2})$', '$2')
            == 02 or day_of_week == 2)}
          goTo: b2b-invoice-lajf
    dependsOn:
      - fork-c82d
  - id: b2b-discount-margin-reporter-hfhe
    name: b2b-discount-margin-reporter
    type: sub-workflow
    spec:
      workflowPath: >-
        git://etl-workflows/wf-service-accounts/hk-fin-ar-prod-svc/ar-reporter#main
      subWorkflowProperties:
        - key: datadate
          value: '{{hive_today_date}}'
        - key: reporter_args
          value: '{{reporter_args}}'
        - key: tag
          value: '{{env_path}}'
        - key: config
          value: '{{reporter_b2b_discount_margin_config}}'
        - key: log4j_config
          value: '{{log4j_config}}'
        - key: submit_user
          value: '{{submitUser}}'
      propagateConfigurations: true
    dependsOn:
      - gen-b2b-discount-margin-report-bilb
  - id: gen-b2b-discount-margin-report-bilb
    name: gen-b2b-discount-margin-report
    type: decision
    spec:
      cases:
        - condition: default
          goTo: join-05d1
        - condition: >-
            ${gen_b2b_discount_margin == 1 and (replaceAll(hive_today_date,
            '^[0-9]{4}([0-9]{2})([0-9]{2})$', '$2') == 17 or
            replaceAll(hive_today_date, '^[0-9]{4}([0-9]{2})([0-9]{2})$', '$2')
            == 02 or day_of_week == 2)}
          goTo: b2b-discount-margin-reporter-hfhe
    dependsOn:
      - fork-c82d
  - id: upload-b2b-invoice-report-dgbj
    name: upload-b2b-invoice-report
    type: shell
    spec:
      commandFile: hdfs:///user/hk-fin-ar-prod-svc/shell_script/erpUpload.sh
      captureOutput: false
      files:
        - hdfs:///user/hk-fin-ar-prod-svc/shell_script/erpUpload.sh
      arguments:
        - '{{is_upload_to_erp}}'
        - '{{erp_connector_server}}'
        - '{{report_upload_date_minus_one_day}}'
        - '212'
        - '213'
    dependsOn:
      - b2b-invoice-lajf
  - id: upload-b2b-discount-margin-report-gike
    name: upload-b2b-discount-margin-report
    type: shell
    spec:
      commandFile: hdfs:///user/hk-fin-ar-prod-svc/shell_script/erpUpload.sh
      captureOutput: false
      files:
        - hdfs:///user/hk-fin-ar-prod-svc/shell_script/erpUpload.sh
      arguments:
        - '{{is_upload_to_erp}}'
        - '{{erp_connector_server}}'
        - '{{report_upload_date_minus_one_day}}'
        - '214'
        - '215'
    dependsOn:
      - b2b-discount-margin-reporter-hfhe
  - id: h2h-aab-ar-mapping-table-bajb
    name: h2h-aab-ar-mapping-table
    type: sub-workflow
    spec:
      workflowPath: >-
        git://etl-workflows/wf-service-accounts/hk-fin-ar-prod-svc/h2h_b2b_ar_with_aab_mapping#main
      subWorkflowProperties:
        - key: finance_b2b_db
          value: '{{finance_b2b_db}}'
        - key: hive_today_datadate
          value: '{{hive_today_date}}'
        - key: hive_yesterday_datadate
          value: '{{hive_yesterday_date}}'
      propagateConfigurations: true
    dependsOn:
      - fork-kebd
  - id: fork-kebd
    name: fork-kebd
    type: fork
    dependsOn: []
  - id: join-dahg
    name: join-dahg
    type: join
    dependsOn:
      - subworkflow-76f4
      - h2h-aab-ar-mapping-table-bajb
schedules:
  - id: b2b-daily-uninvoice-and-daily-booking
    name: b2b-daily-uninvoice-and-daily-booking
    timezone: Asia/Bangkok
    cron: 40 22 * * *
    execution: FIFO
    parameters:
      - name: hive_today_date
        value:
          dateFormat: yyyyMMdd
          offsets:
            - offset: 1
              format: Day
        dataType: date
      - name: hive_yesterday_date
        value:
          dateFormat: yyyyMMdd
          offsets:
            - offset: 0
              format: Day
        dataType: date
      - name: report_upload_date
        value:
          dateFormat: yyyy-MM-dd
          offsets:
            - offset: 1
              format: Day
        dataType: date
      - name: day_of_week
        value:
          dateFormat: u
          offsets: []
        dataType: date
      - name: report_upload_date_minus_one_day
        value:
          dateFormat: yyyy-MM-dd
          offsets: []
        dataType: date
    dataDependencies:
      - table: finance_multiproduct.financial_transactions_by_booking_date
        column: created_log_time
        method: '>='
        offset: -340
        format: Minute
        granularity: Minute
