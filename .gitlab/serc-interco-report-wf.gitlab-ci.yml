.serc_interco_report_wf:e2e_base:
  extends: .serc_interco_report_wf_base
  variables:
    RELATED_WORKFLOW_DIRS: "serc-interco-report-master"
    FINANCE_REPORT_E2E_WF: "serc-interco-report-wf"

serc-interco-report-wf:e2e-run:
  extends:
    - .finance_report_wf:e2e_run
    - .serc_interco_report_wf:e2e_base
  variables:
    INTERCO_DB_NAME: "finance_interco_report_e2e"
    REPORT_NAME: "serc"
    DATADATE: "20250508"
    REPORT_START_MONTH: "202501"
    REPORT_END_MONTH: "202504"
    PROCESS_MONTH: "202505"
    DB_NAME: "finance_agoda_serc_e2e"
    RATE_CONTRACT_ENTITY_FILTER: "'''ACSG1C'''"
    SFTP_PATH_BASE: "/upload/SupportingEntityRevenueCalculation/Reports/E2E"
    VAST_PATH_BASE: "s3a://hk-fin-ar-prod-svc--dev/reports/e2e/interco/serc"
    SKIP_CHECK: "true"
    REVISION: "00"
    THRESHOLD: "0"
    SCR_REPORTS_DB_NAME: "finance_agoda_serc_e2e"
    SCR_DB_NAME: "finance_agoda_serc_e2e"
    FINANCE_DB_NAME: "finance_agoda_serc_e2e"
    FINANCE_ANALYTICS_DB_NAME: "finance_agoda_serc_e2e"
    BI_DW_DB_NAME: "finance_agoda_serc_e2e"
    IS_UPLOAD_TO_ERP: "false"
    ERP_CONNECTOR_SERVER: "dev"
    REPORT_UPLOAD_DATE: "202504"
    WORKFLOW_PARAMS_JSON: |
      {
        "tag": "$WORKFLOW_TAG",
        "interco_db_name": "$INTERCO_DB_NAME",
        "report_name": "$REPORT_NAME",
        "datadate": "$DATADATE",
        "report_start_month": "$REPORT_START_MONTH",
        "report_end_month": "$REPORT_END_MONTH",
        "process_month": "$PROCESS_MONTH",
        "db_name": "$DB_NAME",
        "rate_contract_entity_filter": "$RATE_CONTRACT_ENTITY_FILTER",
        "sftp_path": "$SFTP_PATH_BASE/$E2E_PATH_SUFFIX",
        "vast_path": "$VAST_PATH_BASE/$E2E_PATH_SUFFIX",
        "skip_check": "$SKIP_CHECK",
        "revision": "$REVISION",
        "threshold": "$THRESHOLD",
        "scr_reports_db_name": "$SCR_REPORTS_DB_NAME",
        "scr_db_name": "$SCR_DB_NAME",
        "finance_db_name": "$FINANCE_DB_NAME",
        "finance_analytics_db_name": "$FINANCE_ANALYTICS_DB_NAME",
        "bi_dw_db_name": "$BI_DW_DB_NAME",
        "is_upload_to_erp": "$IS_UPLOAD_TO_ERP",
        "erp_connector_server": "$ERP_CONNECTOR_SERVER",
        "report_upload_date": "$REPORT_UPLOAD_DATE"
      }

serc-interco-report-wf:e2e-fetch-result:
  extends:
    - .finance_report_wf:e2e_fetch_result
    - .serc_interco_report_wf:e2e_base
  needs:
    - serc-interco-report-wf:e2e-run
  timeout: 2h
  variables:
    REPORT_DATE: "202504"
    SFTP_RESULT_DIR_BASE: "/upload/SupportingEntityRevenueCalculation/Reports/E2E"
    SFTP_RESULT_FILE: "AgodaSERC_$REPORT_DATE.00.csv"
    ATTEMPTS: 12
    INTERVAL: 10m

serc-interco-report-wf:e2e-validate:
  extends:
    - .finance_report_wf:e2e_validate
    - .serc_interco_report_wf:e2e_base
  needs:
    - serc-interco-report-wf:e2e-fetch-result
  variables:
    REPLACE_PLACEHOLDERS: "false"
    ENABLE_SORTING: "false"
