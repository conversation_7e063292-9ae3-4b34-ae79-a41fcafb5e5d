.daily_booking_cofunding_report_wf:e2e_base:
  extends: .daily_booking_cofunding_report_wf_base
  variables:
    RELATED_WORKFLOW_DIRS: "wf_fin_report_all_view"
    FINANCE_REPORT_E2E_WF: "daily-booking-cofunding-report-wf"

daily-booking-cofunding-report-wf:e2e-run:
  extends:
    - .finance_report_wf:e2e_run
    - .daily_booking_cofunding_report_wf:e2e_base
  variables:
    AS_OF_DATE: "20250701"
    SRC_FINANCE_TABLE: "finance_cofunding_e2e.valid_daily_transactions"
    DB_NAME: "finance_multiproduct_ar_reports_e2e"
    DESTINATION_FILE_NAME: "DailyBooking_AR_Cofunding_ACS"
    REPORT_NAME: "cofunding_acs"
    REPORT_FORMAT_NAME: "oracle"
    REPORTER_REPORT_TABLE: "report_table_e2e"
    TABLE_CLASSIFICATION: "classified_table_e2e"
    VIEW_AGGREGATION: "aggregated_view_e2e"
    VIEW_CLASSIFICATION: "classified_view_e2e"
    PUBLISH_TO_SFTP: "true"
    PUBLISH_TO_VAST: "true"
    SFTP_PATH_BASE: "/upload/multi-product/ERPInbound_E2E/ar/DailyBooking_AR_Cofunding"
    VAST_PATH_BASE: "s3a://hk-fin-ar-prod-svc/reports/multi-product/erp/e2e/DailyBooking_AR_Cofunding"
    CLASSIFICATION_VAL_EXEC_FLOW_ID: "103325"
    REPORT_TABLE_VAL_EXEC_FLOW_ID: "103325"
    SPARK_QUEUE: "default"
    WORKFLOW_PARAMS_JSON: |
      {
        "tag": "$WORKFLOW_TAG",
        "as_of_date": "$AS_OF_DATE",
        "src_finance_table": "$SRC_FINANCE_TABLE",
        "db_name": "$DB_NAME",
        "destination_file_name": "$DESTINATION_FILE_NAME",
        "report_name": "$REPORT_NAME",
        "report_format_name": "$REPORT_FORMAT_NAME",
        "reporter_report_table": "$REPORTER_REPORT_TABLE",
        "table_classification": "$TABLE_CLASSIFICATION",
        "view_aggregation": "$VIEW_AGGREGATION",
        "view_classification": "$VIEW_CLASSIFICATION",
        "publish_to_sftp": "$PUBLISH_TO_SFTP",
        "publish_to_vast": "$PUBLISH_TO_VAST",
        "sftp_path": "$SFTP_PATH_BASE/$E2E_PATH_SUFFIX",
        "vast_path": "$VAST_PATH_BASE/$E2E_PATH_SUFFIX",
        "classification_val_exec_flow_id": "$CLASSIFICATION_VAL_EXEC_FLOW_ID",
        "report_table_val_exec_flow_id": "$REPORT_TABLE_VAL_EXEC_FLOW_ID",
        "spark_queue": "$SPARK_QUEUE"
      }

daily-booking-cofunding-report-wf:e2e-fetch-result:
  extends:
    - .finance_report_wf:e2e_fetch_result
    - .daily_booking_cofunding_report_wf:e2e_base
  needs:
    - daily-booking-cofunding-report-wf:e2e-run
  variables:
    REPORT_DATE: "20250701"
    SFTP_RESULT_DIR_BASE: "/upload/multi-product/ERPInbound_E2E/ar/DailyBooking_AR_Cofunding"
    SFTP_RESULT_FILE: "DailyBooking_AR_Cofunding_ACS_$REPORT_DATE.csv"
    ATTEMPTS: 120
    INTERVAL: 10

daily-booking-cofunding-report-wf:e2e-validate:
  extends:
    - .finance_report_wf:e2e_validate
    - .daily_booking_cofunding_report_wf:e2e_base
  needs:
    - daily-booking-cofunding-report-wf:e2e-fetch-result
  variables:
    REPLACE_PLACEHOLDERS: "true"
    ENABLE_SORTING: "true"
    PLACEHOLDER_REPLACEMENTS_TEMPLATE: |
      {
        "{{REPORT_DATE}}": "{{REPORT_DATE_VALUE}}"
      }
  before_script:
    - |
      export PLACEHOLDER_REPLACEMENTS="$(
        echo "$PLACEHOLDER_REPLACEMENTS_TEMPLATE" \
          | sed "s|{{REPORT_DATE_VALUE}}|$(date +%Y%m%d)|g"
      )"
    - !reference [.finance_report_wf:e2e_validate, before_script]
