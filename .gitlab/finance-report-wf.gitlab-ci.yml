.finance_report_wf:e2e_base:
  extends: .finance_report_wf_base
  variables:
    FINANCE_REPORT_E2E_WORKFLOW_DIR: "finance-report-e2e-wfs"
    WORKFLOW_DIR: "$PARENT_WORKFLOW_DIR/$FINANCE_REPORT_E2E_WORKFLOW_DIR/$FINANCE_REPORT_E2E_WF"
    RESULT_DIR: "$WORKFLOW_DIR/result"
    E2E_TEMP_BRANCH_PREFIX: "finance-report-wf/e2e/$CI_COMMIT_REF_NAME"
    E2E_TEMP_BRANCH: "$E2E_TEMP_BRANCH_PREFIX-$FINANCE_REPORT_E2E_WF-$CI_PIPELINE_ID"
    # Common path suffix for e2e environments
    E2E_PATH_SUFFIX: "$CI_COMMIT_REF_NAME/$CI_PIPELINE_ID"
    # Common tag for workflows
    WORKFLOW_TAG: "$CI_PIPELINE_ID"

.finance_report_wf:e2e_validate_script:
  script:
    - echo "Starting report validation..."
    - test -f "$REPORT_PATH" || (echo "[ERROR] Actual report file not found at $REPORT_PATH" && exit 1)
    - test -f "$EXPECTED_REPORT_PATH" || (echo "[ERROR] Expected report file not found at $EXPECTED_REPORT_PATH" && exit 1)
    - ACTUAL_FILE="$REPORT_PATH"
    - EXPECTED_FILE="$EXPECTED_REPORT_PATH"
    - |
      if [ "$REPLACE_PLACEHOLDERS" = "true" ] && [ -n "$PLACEHOLDER_REPLACEMENTS" ]; then
        echo "Replacing placeholders in expected file..."
        EXPECTED_FILE="$EXPECTED_REPORT_PATH.processed"
        cp "$EXPECTED_REPORT_PATH" "$EXPECTED_FILE"

        # Loop through each placeholder, using jq to parse the JSON dict
        echo "$PLACEHOLDER_REPLACEMENTS" | jq -r 'to_entries[] | "\(.key)\t\(.value)"' | \
        while IFS=$'\t' read -r placeholder value; do
          # skip empty or null
          [ -z "$placeholder" ] && continue
          [ -z "$value" ] && continue
          
          expanded_value=$(eval echo "$value")
          
          if grep -q "$placeholder" "$EXPECTED_FILE"; then
            echo "Replacing $placeholder with $expanded_value"
            sed "s|$placeholder|$expanded_value|g" "$EXPECTED_FILE" > "$EXPECTED_FILE.tmp"
            mv "$EXPECTED_FILE.tmp" "$EXPECTED_FILE"
          fi
        done
        
        echo "Placeholder replacement completed"
      fi
    - |
      if [ "$ENABLE_SORTING" = "true" ]; then
        echo "Sorting enabled: Sorting and comparing files..."
        sort "$ACTUAL_FILE" > "$ACTUAL_FILE.sorted"
        sort "$EXPECTED_FILE" > "$EXPECTED_FILE.sorted"
        ACTUAL_COMPARE_FILE="$ACTUAL_FILE.sorted"
        EXPECTED_COMPARE_FILE="$EXPECTED_FILE.sorted"
      else
        echo "Sorting disabled: Comparing files directly..."
        ACTUAL_COMPARE_FILE="$ACTUAL_FILE"
        EXPECTED_COMPARE_FILE="$EXPECTED_FILE"
      fi
    - |
      if diff "$ACTUAL_COMPARE_FILE" "$EXPECTED_COMPARE_FILE"; then
        if [ "$ENABLE_SORTING" = "true" ]; then
          echo "[PASSED]: Files are identical after sorting."
        else
          echo "[PASSED]: Files are identical."
        fi
      else
        echo "[FAILED]: Output does not match expected."
        exit 1
      fi

.finance_report_wf:e2e_base_manual:
  extends: .finance_report_wf:e2e_base
  when: manual

.finance_report_wf:e2e_run:
  extends:
    - .git_push
    - .adp_run_workflow
    - .finance_report_wf:e2e_base_manual
  stage: execution
  rules:
    - if: '$CI_MERGE_REQUEST_EVENT_TYPE == "merge_train"'
      when: always
    - !reference [.finance_report_wf:e2e_base_manual, rules]
  variables:
    DATA_PROJECT: hk-fin-ar-prod-svc
    FILES_TO_COMMIT: "."
    COMMIT_MESSAGE: "chore: update workflow branch for e2e"
    DEPLOY_WORKFLOW_WAITING_INTERVAL: 120
    WORKFLOW_DIRS_TO_REPLACE: "$RELATED_WORKFLOW_DIRS $WORKFLOW_DIR"
    WORKFLOW_PARAMS: "$WORKFLOW_PARAMS_JSON"
  before_script:
    - !reference [.git_push, before_script]
    - git checkout -b $E2E_TEMP_BRANCH
    - |
      for wf_dir in $WORKFLOW_DIRS_TO_REPLACE; do
        wf_dir_resolved=$(eval echo "$wf_dir")
        wf_file="$wf_dir_resolved/workflow.yml"
        if [ -f "$wf_file" ]; then
          replacement=$(printf '%s\n' "$E2E_TEMP_BRANCH" | sed 's/[\/&]/\\&/g')
          sed -i "s/#main/#$replacement/g" "$wf_file"
          echo "Updated: $wf_file"
          cat $wf_file
        else
          echo "File not found: $wf_file (skipped)"
        fi
      done
    - GIT_PUSH_BRANCH="$E2E_TEMP_BRANCH"
  script:
    - !reference [.git_push, script]
    - sleep "$DEPLOY_WORKFLOW_WAITING_INTERVAL"
    - !reference [.adp_run_workflow, script]
  after_script:
    - git checkout $CI_COMMIT_REF_NAME
  allow_failure: false

.finance_report_wf:e2e_fetch_result:
  extends:
    - .finance_report_wf:e2e_base
  stage: execution
  variables:
    PROXY_HOST: "sisproxy.hkg.agoda.local"
    PROXY_PORT: "3128"
    SSH_CONFIG_DIR: "$WORKFLOW_DIR/.ssh"
    REPORT_PATH: "$RESULT_DIR/actual.csv"
    SFTP_RESULT_DIR: "$SFTP_RESULT_DIR_BASE/$E2E_PATH_SUFFIX"
    SFTP_PATH: "$SFTP_RESULT_DIR/$SFTP_RESULT_FILE"
    ATTEMPTS: 0
    INTERVAL: 10
  before_script:
    - echo "Installing SFTP and proxy tools..."
    - command -v sshpass || apk add sshpass
    - command -v netcat-openbsd || apk add netcat-openbsd
    - echo "Configuring SSH proxy connection..."
    - mkdir -p $SSH_CONFIG_DIR
    - echo "Host $SFTP_HOST" >> $SSH_CONFIG_DIR/config
    - echo "ProxyCommand nc -X connect -x $PROXY_HOST:$PROXY_PORT %h %p" >> $SSH_CONFIG_DIR/config
    - echo "StrictHostKeyChecking no" >> $SSH_CONFIG_DIR/config
    - echo "UserKnownHostsFile /dev/null" >> $SSH_CONFIG_DIR/config
    - echo "ConnectTimeout 30" >> $SSH_CONFIG_DIR/config
  script:
    - SSH_PASS=$(echo "$SFTP_PASSWORD" | base64 -d)
    - mkdir -p $(dirname "$REPORT_PATH")
    - echo "Polling SFTP for result..."
    - i=1
    - |
      while : ; do
        echo "Checking for file: $SFTP_RESULT_FILE in directory: $SFTP_RESULT_DIR"
        directory_listing=$(echo "ls $SFTP_RESULT_DIR" | sshpass -p "$SSH_PASS" sftp -F $SSH_CONFIG_DIR/config "$SFTP_USERNAME@$SFTP_HOST" 2>/dev/null)
        echo "Directory listing: $directory_listing"
        if echo "$directory_listing" | grep -q "/$SFTP_RESULT_FILE$"; then
          echo "Report found. Downloading..."
          echo "get $SFTP_PATH $REPORT_PATH" | sshpass -p "$SSH_PASS" sftp -F $SSH_CONFIG_DIR/config "$SFTP_USERNAME@$SFTP_HOST" && break
        else
          echo "Not found yet (attempt $i). Waiting."
          if [ "$ATTEMPTS" -gt 0 ] && [ "$i" -gt "$ATTEMPTS" ]; then
            echo "Exceeded $ATTEMPTS attempts. Exiting."
            break
          fi
          sleep "$INTERVAL"
          i=$((i + 1))
        fi
      done
    - test -f "$REPORT_PATH" && echo "Report successfully downloaded to $REPORT_PATH" || (echo "Failed to fetch report from SFTP." && exit 1)
  timeout: 30m
  artifacts:
    paths:
      - $REPORT_PATH

.finance_report_wf:e2e_validate:
  extends:
    - .git_remote_push_set
    - .finance_report_wf:e2e_base
  stage: execution
  variables:
    REPORT_PATH: "$RESULT_DIR/actual.csv"
    EXPECTED_REPORT_PATH: "$RESULT_DIR/expect.csv"
    REPLACE_PLACEHOLDERS: "false"
    PLACEHOLDER_REPLACEMENTS: {}
    ENABLE_SORTING: "true"
  before_script:
    - echo "Installing JSON tool..."
    - command -v jq || apk add jq
  script:
    - !reference [.finance_report_wf:e2e_validate_script, script]
  artifacts:
    paths:
      - $REPORT_PATH

finance-report-wf:e2e-cleanup:
  extends:
    - .git_remote_push_set
    - .finance_report_wf:e2e_base
  stage: .post
  when: manual
  variables:
    ORIGIN_PREFIX: "origin/"
    RELATED_BRANCH_PREFIX: "$E2E_TEMP_BRANCH_PREFIX"
  script:
    - git branch -r
    - |
      prefix="$ORIGIN_PREFIX$RELATED_BRANCH_PREFIX"
      if [ -z "$RELATED_BRANCH_PREFIX" ] || [ "$prefix" = "$ORIGIN_PREFIX" ]; then
        echo "ERROR: RELATED_BRANCH_PREFIX is empty. Aborting to prevent mass deletion!"
        exit 1
      fi
      git branch -r | grep "^ *${prefix}" | sed "s| *$ORIGIN_PREFIX||" | while read branch; do
        echo "Deleting remote branch: $branch"
        git push origin --delete "$branch" || true
      done
    - git branch -r
  allow_failure: false
