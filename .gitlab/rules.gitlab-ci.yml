.changes:
  rules:
    - &finance_report_wf_rule
      if: '$CI_PIPELINE_SOURCE != "schedule" && $CI_PIPELINE_SOURCE != "trigger" && $CI_MERGE_REQUEST_ID'
    - &daily_booking_cofunding_report_wf_rule
      if: '$CI_PIPELINE_SOURCE != "schedule" && $CI_PIPELINE_SOURCE != "trigger" && $CI_MERGE_REQUEST_ID'
      changes:
        - daily-booking-cofunding-report-wf/**/*
        - wf_fin_report_all_view/**/*
        - wf_fin_report_classification/**/*
        - wf_fin_report_aggregation/**/*
        - wf_fin_reporter_generate_report/**/*
    - &serc_interco_report_wf_rule
      if: '$CI_PIPELINE_SOURCE != "schedule" && $CI_PIPELINE_SOURCE != "trigger" && $CI_MERGE_REQUEST_ID'
      changes:
        - serc-interco-report-master/**
        - generic-interco-report-generation-master-wf/**

.finance_report_wf_base:
  rules:
    - *finance_report_wf_rule

.daily_booking_cofunding_report_wf_base:
  rules:
    - *daily_booking_cofunding_report_wf_rule

.serc_interco_report_wf_base:
  rules:
    - *serc_interco_report_wf_rule
