select 
    booking_id,
    promotion_campaign_id,
    paying_aid_1,
    paying_aid_2,
    paying_aid_3,
    booking_date,
    start_date,
    end_date,
    cid,
    sub_supplier_id,
    service_origin_iso3,
    customer_payment_currency,
    fx_customer_payment_rate,
    merchant_of_record_entity,
    revenue_entity,
    rate_contract_entity,
    total_partner_1_funding_amount_usd,
    total_partner_2_funding_amount_usd,
    total_partner_3_funding_amount_usd,
    total_partner_1_previous_invoiced_amount,
    total_partner_2_previous_invoiced_amount,
    total_partner_3_previous_invoiced_amount,
    partner_1_percentage,
    partner_2_percentage,
    partner_3_percentage,
    discount_type,
    product_type,
    keep_until_processing_datadate,
    affiliate_model,
    -1 as datadate,
    cast(null as string) as cc_first_six_digits,
    cast(null as int) as original_payment_category_id,
    cast(null as string) as site_origin,
    cast(null as string) as bin_no,
    cast(null as boolean) as is_valid_bin_no
from finance_cofunding.uninvoiced_transactions;
