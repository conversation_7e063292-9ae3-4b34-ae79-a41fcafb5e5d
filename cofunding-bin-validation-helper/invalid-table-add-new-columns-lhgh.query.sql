select 
    booking_id,
    itinerary_payment_id,
    transaction_id,
    transaction_type,
    reference_type,
    charge_date,
    transaction_date,
    cid,
    booking_date,
    start_date,
    end_date,
    cancellation_date,
    sub_supplier_id,
    supplier_id,
    service_origin_iso3,
    merchant_of_record_entity,
    revenue_entity,
    rate_contract_entity,
    customer_origin_iso2,
    is_test,
    customer_payment_currency,
    supplier_currency,
    fx_customer_payment_rate,
    fx_supplier_rate,
    supplier_name,
    sub_supplier_name,
    mor_name,
    rev_name,
    rce_name,
    booking_month,
    created_log_time,
    source_type,
    booking_datadate_line_number,
    source_datadate,
    uuid,
    adjustment_identifier,
    promotion_amount,
    promotion_amount_usd,
    promotion_amount_customer,
    adjustment_type,
    co_funding_amount,
    co_funding_amount_usd,
    co_funding_amount_customer,
    promotion_campaign_id,
    discount_type,
    campaign_type,
    product_type,
    affiliate_model,
    invalid_reason,
    -1 as datadate,
    cast(null as string) as cc_first_six_digits,
    cast(null as int) as original_payment_category_id,
    cast(null as string) as site_origin,
    cast(null as string) as bin_no,
    cast(null as boolean) as is_valid_bin_no
from finance_cofunding.invalid_daily_transactions;
