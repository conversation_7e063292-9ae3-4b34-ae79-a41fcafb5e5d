workflow:
  id: cofunding-bin-validation-helper
  name: cofunding-bin-validation-helper
  description: ''
  version: 2
actions:
  - id: valid-table-backup-fbli
    name: valid-table-backup
    type: h2h
    spec:
      query: valid-table-backup-fbli.query.sql
      targetTable: '{{resolve_table_name("valid_daily_transactions_backup")}}'
      targetDatabase: finance_cofunding
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: source
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - fork-ehfj
  - id: invalid-table-backup-higd
    name: invalid-table-backup
    type: h2h
    spec:
      query: invalid-table-backup-higd.query.sql
      targetTable: '{{resolve_table_name("invalid_daily_transactions_backup")}}'
      targetDatabase: finance_cofunding
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: source
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - fork-ehfj
  - id: excluded-table-backup-copy-ecbb
    name: excluded-table-backup
    type: h2h
    spec:
      query: excluded-table-backup-copy-ecbb.query.sql
      targetTable: '{{resolve_table_name("excluded_daily_transactions_backup")}}'
      targetDatabase: finance_cofunding
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: source
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - fork-ehfj
  - id: uninvoiced-table-backup-gjlb
    name: uninvoiced-table-backup
    type: h2h
    spec:
      query: uninvoiced-table-backup-gjlb.query.sql
      targetTable: '{{resolve_table_name("uninvoiced_transactions_backup")}}'
      targetDatabase: finance_cofunding
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: source
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - fork-ehfj
  - id: valid-table-add-new-columns-ekei
    name: valid-table-add-new-columns
    type: h2h
    spec:
      query: valid-table-add-new-columns-ekei.query.sql
      targetTable: '{{resolve_table_name("valid_daily_transactions")}}'
      targetDatabase: finance_cofunding
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: source
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - fork-egea
  - id: invalid-table-add-new-columns-lhgh
    name: invalid-table-add-new-columns
    type: h2h
    spec:
      query: invalid-table-add-new-columns-lhgh.query.sql
      targetTable: '{{resolve_table_name("invalid_daily_transactions")}}'
      targetDatabase: finance_cofunding
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: source
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - fork-egea
  - id: excluded-table-add-new-columns-dehi
    name: excluded-table-add-new-columns
    type: h2h
    spec:
      query: excluded-table-add-new-columns-dehi.query.sql
      targetTable: '{{resolve_table_name("excluded_daily_transactions")}}'
      targetDatabase: finance_cofunding
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: source
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - fork-egea
  - id: uninvoiced-table-add-new-columns-bcfh
    name: uninvoiced-table-add-new-columns
    type: h2h
    spec:
      query: uninvoiced-table-add-new-columns-bcfh.query.sql
      targetTable: '{{resolve_table_name("uninvoiced_transactions")}}'
      targetDatabase: finance_cofunding
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: source
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - fork-egea
  - id: fork-ehfj
    name: fork-ehfj
    type: fork
    dependsOn: []
  - id: join-glic
    name: join-glic
    type: join
    dependsOn:
      - invalid-table-backup-higd
      - valid-table-backup-fbli
      - excluded-table-backup-copy-ecbb
      - uninvoiced-table-backup-gjlb
  - id: fork-egea
    name: fork-egea
    type: fork
    dependsOn:
      - join-glic
  - id: join-ddhd
    name: join-ddhd
    type: join
    dependsOn:
      - valid-table-add-new-columns-ekei
      - invalid-table-add-new-columns-lhgh
      - excluded-table-add-new-columns-dehi
      - uninvoiced-table-add-new-columns-bcfh
params_profile:
  - name: default
    params: []
  - name: dev
    params: []
schedules: []
