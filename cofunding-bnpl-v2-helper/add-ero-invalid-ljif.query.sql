select 
    main.booking_id,
    main.itinerary_payment_id,
    main.transaction_id,
    main.transaction_type,
    main.reference_type,
    main.charge_date,
    main.transaction_date,
    main.cid,
    main.booking_date,
    main.start_date,
    main.end_date,
    main.cancellation_date,
    main.sub_supplier_id,
    main.supplier_id,
    main.service_origin_iso3,
    main.merchant_of_record_entity,
    main.revenue_entity,
    main.rate_contract_entity,
    main.customer_origin_iso2,
    main.is_test,
    main.customer_payment_currency,
    main.supplier_currency,
    main.fx_customer_payment_rate,
    main.fx_supplier_rate,
    main.supplier_name,
    main.sub_supplier_name,
    main.mor_name,
    main.rev_name,
    main.rce_name,
    main.booking_month,
    main.created_log_time,
    main.source_type,
    main.booking_datadate_line_number,
    main.source_datadate,
    main.uuid,
    main.adjustment_identifier,
    main.promotion_amount,
    main.promotion_amount_usd,
    main.promotion_amount_customer,
    main.adjustment_type,
    main.co_funding_amount,
    main.co_funding_amount_usd,
    main.co_funding_amount_customer,
    main.promotion_campaign_id,
    main.discount_type,
    main.campaign_type,
    main.product_type,
    main.affiliate_model,
    main.invalid_reason,
    main.cc_first_six_digits,
    main.original_payment_category_id,
    main.site_origin,
    main.bin_no,
    main.is_valid_bin_no,
    main.datadate,
    fb.exchange_rate_option
from  finance_cofunding.invalid_daily_transactions as main
left join bi_dw.fact_booking as fb
    on main.booking_id = fb.booking_id
where main.datadate = {{datadate}};