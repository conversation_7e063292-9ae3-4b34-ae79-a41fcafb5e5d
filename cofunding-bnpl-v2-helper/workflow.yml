workflow:
  id: cofunding-bnpl-v2-helper
  name: cofunding-bnpl-v2-helper
  description: ''
  version: 2
actions:
  - id: backup-valid-feab
    name: backup-valid
    type: h2h
    spec:
      query: backup-valid-feab.query.sql
      targetTable: '{{resolve_table_name("valid_daily_transactions_backup")}}'
      targetDatabase: finance_cofunding
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: source
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - fork-lblf
  - id: backup-invalid-clkd
    name: backup-invalid
    type: h2h
    spec:
      query: backup-invalid-clkd.query.sql
      targetTable: '{{resolve_table_name("invalid_daily_transactions_backup")}}'
      targetDatabase: finance_cofunding
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: source
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - fork-lblf
  - id: backup-excluded-ided
    name: backup-excluded
    type: h2h
    spec:
      query: backup-excluded-ided.query.sql
      targetTable: '{{resolve_table_name("excluded_daily_transactions_backup")}}'
      targetDatabase: finance_cofunding
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: source
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - fork-lblf
  - id: fork-lblf
    name: fork-lblf
    type: fork
    dependsOn: []
  - id: join-lfgi
    name: join-lfgi
    type: join
    dependsOn:
      - backup-valid-feab
      - backup-invalid-clkd
      - backup-excluded-ided
  - id: add-ero-valid-hkhc
    name: add-ero-valid
    type: h2h
    spec:
      query: add-ero-valid-hkhc.query.sql
      targetTable: '{{resolve_table_name("valid_daily_transactions")}}'
      targetDatabase: finance_cofunding
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: source
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - fork-bkef
  - id: add-ero-invalid-ljif
    name: add-ero-invalid
    type: h2h
    spec:
      query: add-ero-invalid-ljif.query.sql
      targetTable: '{{resolve_table_name("invalid_daily_transactions")}}'
      targetDatabase: finance_cofunding
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: source
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - fork-bkef
  - id: add-ero-excluded-acal
    name: add-ero-excluded
    type: h2h
    spec:
      query: add-ero-excluded-acal.query.sql
      targetTable: '{{resolve_table_name("excluded_daily_transactions")}}'
      targetDatabase: finance_cofunding
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: source
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - fork-bkef
  - id: fork-bkef
    name: fork-bkef
    type: fork
    dependsOn:
      - join-lfgi
  - id: join-kjib
    name: join-kjib
    type: join
    dependsOn:
      - add-ero-valid-hkhc
      - add-ero-invalid-ljif
      - add-ero-excluded-acal
params_profile:
  - name: default
    params:
      - name: datadate
        value: __deferred
        dataType: date
  - name: dev
    params: []
schedules: []
