workflow:
  id: backfillb2baging
  name: BackfillB2BAging
  description: ''
  version: 2
actions:
  - id: backfill-jjhc
    name: backfill
    type: sub-workflow
    spec:
      workflowPath: >-
        git://etl-workflows/wf-service-accounts/hk-fin-ar-prod-svc/b2b-ar-reconciliator#main
      subWorkflowProperties:
        - key: finance_b2b_db
          value: finance_b2b
        - key: env
          value: production
        - key: ar_recon_config
          value: sg/b2b/release.conf
        - key: reporter_args
          value: slack=true
        - key: reporter_multiproduct_daily_config
          value: sg/b2b/release_multiproduct_daily.conf
        - key: hive_today_date
          value: '{{hive_today_date}}'
        - key: from
          value: '{{from}}'
        - key: to
          value: '{{to}}'
      propagateConfigurations: true
    dependsOn: []
params_profile:
  - name: default
    params:
      - name: hive_today_date
        value: __deferred
        dataType: date
      - name: from
        value: __deferred
        dataType: date
      - name: to
        value: __deferred
        dataType: date
  - name: dev
    params: []
schedules: []
