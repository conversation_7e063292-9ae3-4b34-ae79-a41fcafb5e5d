workflow:
  id: backup_bhi_exchange_rate_duplicate
  name: Backup_bhi_exchange_rate_duplicate
  description: ''
  version: 2
actions:
  - id: backup-clhh
    name: backup
    type: h2h
    spec:
      query: backup-clhh.query.sql
      targetTable: '{{resolve_table_name("bhi_exchange_rate_backup_202402")}}'
      targetDatabase: finance
      partitionBy:
        staticValue:
          columns:
            - name: master
              type: string
              value: master
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn: []
  - id: fix-dup-kkbj
    name: fix dup
    type: h2h
    spec:
      query: fix-dup-kkbj.query.sql
      targetTable: '{{resolve_table_name("bhi_exchange_rate_temp")}}'
      targetDatabase: finance
      partitionBy:
        columnBased:
          columns:
            - name: datamonth
              type: int
            - name: source
              type: string
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - backup-clhh
  - id: overwrite-to-prod-table-agil
    name: overwrite to prod table
    type: h2h
    spec:
      query: overwrite-to-prod-table-agil.query.sql
      targetTable: '{{resolve_table_name("bhi_exchange_rate")}}'
      targetDatabase: finance
      partitionBy:
        columnBased:
          columns:
            - name: datamonth
              type: int
            - name: source
              type: string
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - fix-dup-kkbj
params_profile:
  - name: default
    params: []
  - name: dev
    params: []
schedules: []
