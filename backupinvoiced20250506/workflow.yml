workflow:
  id: backupinvoiced20250506
  name: backupinvoiced20250506
  description: ''
  version: 2
actions:
  - id: backup-laag
    name: backup
    type: h2h
    spec:
      query: backup-laag.query.sql
      targetTable: invoiced_transactions_by_invoice_date_backup_20250506
      targetDatabase: finance_cofunding
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn: []
params_profile:
  - name: default
    params: []
  - name: dev
    params: []
schedules: []
