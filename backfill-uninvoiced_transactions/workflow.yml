workflow:
  id: backfill-uninvoiced_transactions
  name: backfill uninvoiced_transactions
  description: ''
  version: 2
actions:
  - id: backfill-uninvoiced-fb60
    name: backfill uninvoiced
    type: h2h
    spec:
      query: backfill-uninvoiced-fb60.query.sql
      targetTable: uninvoiced_transactions
      targetDatabase: finance_b2b
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: source
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        append: {}
    dependsOn: []
params_profile:
  - name: default
    params:
      - name: booking_id
        value: __deferred
        dataType: string
  - name: dev
    params: []
schedules: []
