workflow:
  id: ar_commission_file_downloader
  name: ar_commission_file_downloader
  description: ''
  version: 2
actions:
  - id: downloader-aidf
    name: agency_commission_outstanding_downloader
    type: spark
    spec:
      sparkVersion: 3.3.1
      image: >-
        reg-sg.agodadev.io/gitlab/financeplatform/reconciliation-platform/external-data-pipeline/downloader
      mainClass: com.agoda.finance.Main
      tag: release
      dependencies:
        jars:
          - local:///opt/app/assembly/dependency_cache.jar
        files:
          - local:///opt/app/deps/files/log4j_adp.properties
      arguments:
        - >-
          mode=date savemode=overwrite filedate={{datadate}}
          partitiondate={{datadate}}
      driver:
        cores: '1'
        memory: 20g
        memoryOverhead: 16384m
        javaOptions:
          - '-Dconfig.resource={{config_commission_outstanding}}'
      executor:
        cores: '6'
        memory: 10g
        memoryOverhead: 3072m
        instances: '15'
        javaOptions:
          - '-Dconfig.resource={{config_commission_outstanding}}'
      mainApplicationFile: local:///opt/app/assembly/assembly.jar
    dependsOn: []
  - id: agency-commercial-invoice-downloader-aklf
    name: agency_commercial_invoice_downloader
    type: spark
    spec:
      sparkVersion: 3.3.1
      image: >-
        reg-sg.agodadev.io/gitlab/financeplatform/reconciliation-platform/external-data-pipeline/downloader
      mainClass: com.agoda.finance.Main
      tag: release
      dependencies:
        jars:
          - local:///opt/app/assembly/dependency_cache.jar
        files:
          - local:///opt/app/deps/files/log4j_adp.properties
      arguments:
        - mode=date savemode=overwrite filedate={{datadate}}
      driver:
        cores: '1'
        memory: 20g
        memoryOverhead: 16384m
        javaOptions:
          - '-Dconfig.resource={{config_commercial_invoice}}'
      executor:
        cores: '6'
        memory: 10g
        memoryOverhead: 3072m
        instances: '15'
        javaOptions:
          - '-Dconfig.resource={{config_commercial_invoice}}'
      mainApplicationFile: local:///opt/app/assembly/assembly.jar
    dependsOn:
      - downloader-aidf
params_profile:
  - name: default
    params:
      - name: config_commission_outstanding
        value: instances/agency_commission_outstanding_file/release.conf
        dataType: string
      - name: config_commercial_invoice
        value: instances/agency_commercial_invoice_file/release.conf
        dataType: string
      - name: datadate
        value: _deferred
        dataType: date
  - name: dev
    params: []
schedules:
  - id: scheduler-aheh
    name: scheduler
    cron: 0 22 * * *
    parameters:
      - name: datadate
        value:
          dateFormat: yyyy-MM-dd
          offsets:
            - offset: -1
              format: Day
        dataType: date
