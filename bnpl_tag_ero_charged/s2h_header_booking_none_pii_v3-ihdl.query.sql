SELECT header_id,
       booking_id,
       hotel_id,
       member_id,
       site_id,
       affiliate_model_id,
       room_count,
       night_count,
       arrival,
       departure,
       booked,
       CAST(FORMAT(CAST(booked AS DATE), 'yyyyMMdd') AS INT) as booked_datadate,
       rec_status,
       rec_created_when,
       rec_created_by,
       rec_modify_when,
       rec_modify_by,
       bnpl,
       payment_model,
       dmc_id,
       external_reference,
       rateplan_id,
       cancellation_policy_code,
       hotel_payment_condition_id,
       is_firedrill,
       is_nonrefundable,
       cc_id,
       tracking_tag,
       offer_number,
       pre_booking_id,
       book_status_id,
       cost_center_id,
       sell_tag_id,
       charge_option_id,
       exchange_rate_option,
       no_of_adults,
       no_of_children,
       is_payable,
       occupancy,
       inventory_type_id,
       is_no_show,
       is_agoda_agency,
       non_refundable_payment_condition_id,
       bnpl_fully_auth_date,
       bnpl_minimum_payment_date
FROM agoda_finance.dbo.header_booking_none_pii_v4
WHERE exchange_rate_option in (1,4) and $CONDITIONS