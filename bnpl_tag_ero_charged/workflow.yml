workflow:
  id: bnpl_tag_ero_charged
  name: bnpl_tag_ero_charged
  description: ''
  version: 2
actions:
  - id: s2h-header-booking-none-pii-v3-ihdl
    name: S2H_header_booking_none_pii_v3
    type: s2h
    spec:
      query: s2h_header_booking_none_pii_v3-ihdl.query.sql
      targetTable: header_booking_none_pii_v4
      targetDatabase: finance_bnpl_v2
      partitionBy:
        columnBased:
          columns:
            - name: booked_datadate
              type: source
            - name: exchange_rate_option
              type: source
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
      sqlConnection:
        id: sgfinancestandby_-_agoda_finance_-_finance_platform_rw_user
      sparkSetting:
        driverMemory: 16g
        executorNumber: '40'
        executorCore: '4'
        executorMemory: 16g
    dependsOn: []
params_profile:
  - name: default
    params: []
  - name: dev
    params: []
schedules: []
