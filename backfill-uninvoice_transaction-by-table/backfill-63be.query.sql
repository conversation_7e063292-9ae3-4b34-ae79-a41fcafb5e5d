with total_invoiced as (
select  booking_id 
, sum(shared_margin_amount_invoice_currency) as total_previous_shared_margin_amount_invoice_currency
, sum(shared_margin_amount_data_feed_currency) as total_previous_shared_margin_amount_data_feed_currency
, sum(shared_margin_amount_functional_currency) as total_previous_shared_margin_amount_functional_currency
, sum(discount_amount_invoice_currency) as total_previous_discount_amount_invoice_currency
, sum(discount_amount_data_feed_currency) as total_previous_discount_amount_data_feed_currency
, sum(discount_amount_functional_currency) as total_previous_discount_amount_functional_currency
, sum(amount_due_invoice_currency) as total_previous_amount_due_invoice_currency
, sum(amount_due_data_feed_currency) as total_previous_amount_due_data_feed_currency
, sum(amount_due_functional_currency) as total_previous_amount_due_functional_currency
, sum(invoiced_customer_payment) as total_invoiced_customer_payment
, sum(invoiced_customer_payment_usd) as total_invoiced_customer_payment_usd
, sum(invoiced_supplier_margin_customer) as total_invoiced_supplier_margin_customer
, sum(invoiced_supplier_margin_usd) as total_invoiced_supplier_margin_usd
from finance_b2b.invoiced_transactions_by_invoice_date 
group by booking_id 
)
SELECT v.booking_id,
CAST(max(v.cid) as string) as cid,
max(v.booking_date) as booking_date,
max(v.start_date) as start_date,
max(v.end_date) as end_date,
max(v.sub_supplier_id) as sub_supplier_id,
max(v.service_origin_id) as service_origin_id,
max(c.country_iso2) as service_origin_iso2,
max(v.service_origin_iso3) as service_origin_iso3,
max(v.customer_payment_currency) as customer_payment_currency,
CAST(null as DECIMAL(18,8)) as fx_customer_payment_rate,
max(v.tracking_tag) as tracking_tag,
max(v.merchant_of_record_entity) as merchant_of_record_entity,
max(v.merchant_of_record_entity_type) as merchant_of_record_entity_type,
max(v.revenue_entity) as revenue_entity,
max(v.revenue_entity_type) as revenue_entity_type,
max(v.rate_contract_entity) as rate_contract_entity,
max(v.rate_contract_entity_type) as rate_contract_entity_type,
CAST(sum(v.customer_payment) as DECIMAL(18,8)) as total_customer_payment,
CAST(sum(v.customer_payment_usd) as DECIMAL(18,8)) as total_customer_payment_usd,
CAST(sum(v.supplier_margin_customer) as DECIMAL(18,8)) as total_supplier_margin_customer,
CAST(sum(v.supplier_margin_usd) as DECIMAL(18,8)) as total_supplier_margin_usd,
CAST(sum(v.supplier_processing_fee_customer) as DECIMAL(18,8)) as total_supplier_processing_fee_customer,
CAST(sum(v.supplier_processing_fee_usd) as DECIMAL(18,8)) as total_supplier_processing_fee_usd,
CAST(sum(v.supplier_tax_customer) as DECIMAL(18,8)) as total_supplier_tax_customer,
CAST(sum(v.supplier_tax_usd) as DECIMAL(18,8)) as total_supplier_tax_usd,
CAST(sum(v.supplier_tax_pay_to_government_customer) as DECIMAL(18,8)) as total_supplier_tax_pay_to_government_customer,
CAST(sum(v.supplier_tax_pay_to_government_usd) as DECIMAL(18,8)) as total_supplier_tax_pay_to_government_usd,
CAST(COALESCE(MAX(invoiced.total_previous_shared_margin_amount_invoice_currency), 0) as DECIMAL(18,8)) as total_previous_shared_margin_amount_invoice_currency,
CAST(COALESCE(MAX(invoiced.total_previous_shared_margin_amount_data_feed_currency), 0) as DECIMAL(18,8)) as total_previous_shared_margin_amount_data_feed_currency,
CAST(COALESCE(MAX(invoiced.total_previous_shared_margin_amount_functional_currency), 0) as DECIMAL(18,8)) as total_previous_shared_margin_amount_functional_currency,
CAST(COALESCE(MAX(invoiced.total_previous_amount_due_invoice_currency), 0) as DECIMAL(18,8)) as total_previous_amount_due_invoice_currency,
CAST(COALESCE(MAX(invoiced.total_previous_amount_due_data_feed_currency), 0) as DECIMAL(18,8)) as total_previous_amount_due_data_feed_currency,
CAST(COALESCE(MAX(invoiced.total_previous_amount_due_functional_currency), 0) as DECIMAL(18,8)) as total_previous_amount_due_functional_currency,
CAST(COALESCE(MAX(invoiced.total_invoiced_customer_payment), 0) as DECIMAL(18,8)) as total_invoiced_customer_payment,
CAST(COALESCE(MAX(invoiced.total_invoiced_customer_payment_usd), 0) as DECIMAL(18,8)) as total_invoiced_customer_payment_usd,
CAST(COALESCE(MAX(invoiced.total_invoiced_supplier_margin_customer), 0) as DECIMAL(18,8)) as total_invoiced_supplier_margin_customer,
CAST(COALESCE(MAX(invoiced.total_invoiced_supplier_margin_usd), 0) as DECIMAL(18,8)) as total_invoiced_supplier_margin_usd,
max(v.whitelabel_id) as whitelabel_id,
CAST(20 as int) as payment_type_id,
CAST(3 as int) as transaction_type,
max(v.payment_model) as payment_model,
max(v.product_type) as product_type,
max(v.affiliate_model) as affiliate_model,
max(CAST(COALESCE(h.state_id, 0) as bigint)) as state_id,
CASE
               WHEN max(v.revenue_entity) = 5659 THEN 5632
               ELSE max(v.revenue_entity)
END as calculated_revenue_entity,
CAST(null as int) as keep_until_processing_datadate,
max(v.whitelabel_group_id) as whitelabel_group_id,
CAST(COALESCE(MAX(invoiced.total_previous_discount_amount_invoice_currency), 0) as DECIMAL(18,8)) as total_previous_discount_amount_invoice_currency,
CAST(COALESCE(MAX(invoiced.total_previous_discount_amount_data_feed_currency), 0) as DECIMAL(18,8)) as total_previous_discount_amount_data_feed_currency,
CAST(COALESCE(MAX(invoiced.total_previous_discount_amount_functional_currency), 0) as DECIMAL(18,8)) as total_previous_discount_amount_functional_currency,
CAST(0 as DECIMAL(18,8)) as total_supplier_ess_customer,
CAST(0 as DECIMAL(18,8)) as total_supplier_ess_usd,
CAST(0 as DECIMAL(18,8)) as total_invoiced_supplier_ess_customer,
CAST(0 as DECIMAL(18,8)) as total_invoiced_supplier_ess_usd,
CAST( 20240118 as int) as datadate
FROM finance_multiproduct.financial_transactions_by_booking_date v
LEFT OUTER JOIN total_invoiced as invoiced on v.booking_id = invoiced.booking_id
LEFT OUTER JOIN bi_dw.dim_country c on v.service_origin_iso3 = c.country_iso
LEFT OUTER JOIN bi_dw.dim_hotel_static h on v.sub_supplier_id = h.hotel_id
WHERE v.booking_id in 
            (SELECT distinct booking_id FROM finance_b2b.invoiced_transactions_by_booking_date
            where invoice_date = '2024-01-16T00:00:00' 
            and invoice_method_id = 5
            and booking_id not in (select booking_id from finance_b2b.uninvoiced_transactions where datadate = 20240118 ))
GROUP BY v.booking_id