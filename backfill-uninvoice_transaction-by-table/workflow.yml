workflow:
  id: backfill-uninvoice_transaction-by-table
  name: backfill uninvoice_transaction by table
  description: ''
  version: 2
actions:
  - id: backfill-63be
    name: backfill
    type: h2h
    spec:
      query: backfill-63be.query.sql
      targetTable: uninvoiced_transactions
      targetDatabase: finance_b2b
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: source
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        append: {}
      alterTableSchema: true
      maxDuration: 3600
    dependsOn: []
    maxRetry: '0'
params_profile:
  - name: default
    params: []
  - name: dev
    params: []
schedules: []
