workflow:
  id: advance-cost-paid-report
  name: Advance Cost Paid Report
  description: ''
  version: 2
actions:
  - id: generate-report-caka
    name: generate report
    maxRetry: 0
    retryInterval: 1
    dependsOn: []
    type: spark
    spec:
      mainApplicationFile: local:///opt/app/assembly/assembly.jar
      image: >-
        reg-sg.agodadev.io/gitlab/financeplatform/core-platform/finance-reporter/apreporter
      tag: release
      additionalSparkConfigurations:
        - key: spark.executor.java-opts
          value: >-
            -XX:+CMSClassUnloadingEnabled -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/agoda/heap_dump/ -Dhadoop.postgres.connectionString=******************************************************** -Detl.services.refresh.server-location=PROD_SGP
        - key: spark.serializer
          value: org.apache.spark.serializer.KryoSerializer
        - key: spark.hadoop.hadoop.security.credential.provider.path
          value: ''
        - key: spark.executor.extraJavaOptions
          value: >-
            -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintGCApplicationStoppedTime -XX:+PrintGCCause -verbose:gc
        - key: spark.kerberos.access.hadoopFileSystems
          value: hdfs://nameservice1,hdfs://nameservice2
        - key: spark.eventLog.enabled
          value: 'true'
        - key: spark.executor.memoryOverhead
          value: '1024'
        - key: spark.sql.adaptive.enabled
          value: 'true'
        - key: spark.yarn.populateHadoopClasspath
          value: 'true'
        - key: spark.driver.maxResultSize
          value: 25g
        - key: spark.shuffle.service.enabled
          value: 'true'
        - key: spark.sql.hive.metastore.token.signature
          value: hiveserver2ClientToken
        - key: spark.sql.parquet.int96RebaseModeInRead
          value: LEGACY
        - key: spark.hadoop.hive.execution.engine
          value: mr
        - key: spark.sql.hive.metastore.jars
          value: /usr/hdp/current/hive-client/lib/*:./*
        - key: spark.network.timeout
          value: '600'
        - key: spark.rpc.askTimeout
          value: '600'
        - key: spark.driver.memory
          value: 2G
        - key: spark.executor.instances
          value: '2'
        - key: spark.storage.memoryMapThreshold
          value: '2097152'
        - key: spark.logConf
          value: 'true'
        - key: spark.driver.memoryOverhead
          value: '1024'
        - key: spark.sql.adaptive.coalescePartitions.enabled
          value: 'true'
        - key: spark.sql.legacy.timeParserPolicy
          value: LEGACY
        - key: spark.sql.broadcastTimeout
          value: '3000'
        - key: spark.yarn.maxAppAttempts
          value: '1'
        - key: spark.executor.memory
          value: 6G
        - key: spark.executor.cores
          value: '3'
        - key: spark.sql.parquet.writeLegacyFormat
          value: 'true'
        - key: spark.sql.parquet.int96RebaseModeInWrite
          value: LEGACY
        - key: hive.metastore.disallow.incompatible.col.type.changes
          value: 'false'
        - key: spark.driver.java-opts
          value: >-
            -XX:+CMSClassUnloadingEnabled -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/agoda/heap_dump/ -Dhadoop.postgres.connectionString=******************************************************** -Detl.services.refresh.server-location=PROD_SGP
        - key: spark.sql.hive.metastore.version
          value: 3.1.1
      sparkVersion: 3.3.1
      mainClass: com.agoda.finance.apReporter.Main
      driver:
        memoryOverhead: 1024m
        javaOptions:
          - '-XX:+CMSClassUnloadingEnabled'
          - '-XX:+HeapDumpOnOutOfMemoryError'
          - '-XX:HeapDumpPath=/agoda/heap_dump/'
          - >-
            -Dhive.metastore.filter.hook=com.agoda.adp.hadoop.hive.client.hook.AdpFilterHook
          - '-Dconfig.strategy=com.agoda.ml.config.MultiResourceStrategy'
          - >-
            -Dconfig.resource=sg/aprecon/mpap.{{env}}/{{env}}_multiproduct_ap_monthly.conf
          - '-Dlog4j.configuration=log/log4j_adp.properties'
        memory: 2g
      dynamicAllocation: {}
      executor:
        instances: '2'
        cores: '3'
        memory: 6g
        memoryOverhead: 1024m
        javaOptions:
          - '-XX:+CMSClassUnloadingEnabled'
          - '-XX:+HeapDumpOnOutOfMemoryError'
          - '-XX:HeapDumpPath=/agoda/heap_dump/'
          - '-Dconfig.strategy=com.agoda.ml.config.MultiResourceStrategy'
          - >-
            -Dconfig.resource=sg/aprecon/mpap.{{env}}/{{env}}_multiproduct_ap_monthly.conf
          - '-Dlog4j.configuration=log/log4j_adp.properties'
      dependencies:
        jars:
          - local:///opt/app/assembly/dependency_cache.jar
      arguments:
        - date={{datadate}}
  - id: upload-to-erp-echk
    name: should_upload_to_erp
    type: decision
    spec:
      cases:
        - condition: default
          goTo: End
        - condition: ${upload_acp_to_erp==true}
          goTo: upload-to-erp-ccil
    dependsOn:
      - send-success-email-kkhk
  - id: upload-to-erp-ccil
    name: upload_to_erp
    type: shell
    spec:
      commandFile: hdfs:///user/hk-fin-ar-prod-svc/shell_script/erpUpload.sh
      captureOutput: false
      arguments:
        - '{{upload_acp_to_erp}}'
        - '{{erp_connector_server}}'
        - '{{report_upload_date}}'
        - '205'
        - '206'
        - '207'
      files:
        - hdfs:///user/hk-fin-ar-prod-svc/shell_script/erpUpload.sh
    dependsOn:
      - upload-to-erp-echk
  - id: send-success-email-kkhk
    name: send_success_email
    type: email
    spec:
      to:
        - <EMAIL>
      cc: []
      bcc: []
      subject: apReporter success
      attachments: []
      contentType: text/plain
      bodyFile: send_success_email-kkhk.body.txt
    dependsOn:
      - generate-report-caka
params_profile:
  - name: default
    params:
      - name: env
        value: _deferred
        dataType: string
      - name: upload_acp_to_erp
        value: '0'
        dataType: string
      - name: report_upload_date
        value: _deferred
        dataType: string
      - name: datadate
        value: _deferred
        dataType: date
      - name: erp_connector_server
        value: _deferred
        dataType: string
  - name: dev
    params: []
schedules: []
