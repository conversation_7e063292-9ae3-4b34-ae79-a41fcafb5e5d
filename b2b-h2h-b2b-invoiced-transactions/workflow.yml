workflow:
  id: b2b-h2h-b2b-invoiced-transactions
  name: _B2B__H2H_B2B_invoiced_transactions
  description: B2B invoiced (invoice_header + invoice_transaction)
  version: 2
params_profile:
  - name: default
    params:
      - name: datadate
        value: __deferred
        dataType: date
      - name: target_database
        value: finance_b2b
        dataType: string
  - name: dev
    params:
      - name: target_database
        value: finance_b2b_dev
        dataType: string
actions:
  - id: etl-5473
    name: B2B invoiced partition by invoice_date (etl-5473)
    type: h2h
    spec:
      query: b2b-invoiced-partition-by-invoice-date-etl-5473.query.sql
      targetTable: invoiced_transactions_by_invoice_date
      targetDatabase: '{{target_database}}'
      quilliup:
        enableQuilliup: false
      mergeBy:
        columnBased:
          isPartialUpdate: false
          keyColumns:
            - invoice_transaction_id
          checkDuplicate: true
          breakMergeInputs: false
      sparkSetting:
        compressionCodecIs: snappy
        enableCheckpoint: false
      additionalSparkConfigurations:
        - key: spark.sql.parquet.writeLegacyFormat
          value: 'true'
        - key: spark.sql.legacy.timeParserPolicy
          value: LEGACY
      enableValidationRule: true
      trackingColumns:
        - datadate
      computeStatsEnabled: false
      alterTableSchema: true
      loggingTimesEnabled: false
      partitionFolderTemplate: '{partition_value}'
      deleteOldVersionsOlderThan: 192
      keepOldVersionsNewerThan: 8
      dataOutputFormat: parquet
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      distributeBy:
        auto: {}
    maxRetry: 3
    retryInterval: 10
    dependsOn:
      - fork-a223
  - id: etl-1473
    name: B2B invoiced partition by booking_date (etl-1473)
    type: h2h
    spec:
      query: b2b-invoiced-partition-by-booking-date-etl-1473.query.sql
      targetTable: invoiced_transactions_by_booking_date
      targetDatabase: '{{target_database}}'
      quilliup:
        enableQuilliup: false
      mergeBy:
        columnBased:
          isPartialUpdate: false
          keyColumns:
            - invoice_transaction_id
          checkDuplicate: true
          breakMergeInputs: false
      sparkSetting:
        compressionCodecIs: snappy
        enableCheckpoint: false
      additionalSparkConfigurations:
        - key: spark.sql.parquet.writeLegacyFormat
          value: 'true'
        - key: spark.sql.legacy.timeParserPolicy
          value: LEGACY
      enableValidationRule: true
      trackingColumns:
        - datadate
      computeStatsEnabled: false
      alterTableSchema: true
      loggingTimesEnabled: false
      partitionFolderTemplate: '{partition_value}'
      deleteOldVersionsOlderThan: 192
      keepOldVersionsNewerThan: 8
      dataOutputFormat: parquet
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      distributeBy:
        auto: {}
    maxRetry: 3
    retryInterval: 10
    dependsOn:
      - fork-a223
  - id: email-461a
    type: email
    name: email-461a
    spec:
      to:
        - <EMAIL>
      subject: Passed Invoice Transaction Daily completed successfully
      bodyFile: email_body_file_email-461a.txt
      contentType: text/plain
    dependsOn:
      - etl-5473
  - id: email-097a
    type: email
    name: email-097a
    spec:
      to:
        - <EMAIL>
      subject: Passed Invoice Transaction Daily completed successfully - booking date
      bodyFile: email_body_file_email-097a.txt
      contentType: text/plain
    dependsOn:
      - etl-1473
  - id: fork-a223
    name: fork-a223
    type: fork
    dependsOn: []
  - id: join-a0e2
    name: join-a0e2
    type: join
    dependsOn:
      - email-461a
      - email-097a
schedules:
  - id: b2b-h2h-b2b-invoiced-transactions
    name: _B2B__H2H_B2B_invoiced_transactions
    timezone: Antarctica/Davis
    cron: 0 18 * * *
    execution: FIFO
    parameters:
      - name: datadate
        value:
          dateFormat: yyyyMMdd
          offsets:
            - offset: 0
              format: Hour
        dataType: date
