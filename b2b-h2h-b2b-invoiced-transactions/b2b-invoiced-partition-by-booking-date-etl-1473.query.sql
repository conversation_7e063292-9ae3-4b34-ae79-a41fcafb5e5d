SELECT
    H.invoice_id 
   ,H.invoice_method_id
   ,H.reference_number
   ,H.payer_source_id
   ,CAST(H.invoice_date as timestamp) as invoice_date
   ,CAST(H.period_from as timestamp) as period_from
   ,CAST(H.period_to as timestamp) as period_to
   ,H.currency_code 
   ,ROUND(CAST(H.amount as double), 2) as header_amount
   ,ROUND(CAST(H.net_amount as double), 2) as net_amount
   ,H.invoice_status_id 
   ,H.document_id 
   ,H.rec_status 
   ,CAST(H.rec_created_when as timestamp) as rec_created_when 
   ,H.rec_created_by 
   ,CAST(H.rec_modified_when as timestamp) as rec_modified_when
   ,H.rec_modify_by
  ,CAST(regexp_replace(get_json_object(H.additional_data, '$.affiliateSiteId'), ',', '') as int) as affiliate_site_id
   ,CAST(regexp_replace(get_json_object(H.additional_data, '$.creditTerm'), ',', '') as int) as credit_term
   ,ROUND(CAST(regexp_replace(get_json_object(H.additional_data, '$.totalGstAmount'), ',', '') as double), 2) as total_gst_amount
   ,get_json_object(H.additional_data, '$.functionalCurrency') as functional_currency
   ,COALESCE(get_json_object(H.additional_data, '$.customerName'), get_json_object(H.additional_data, '$.partnerName'))  as partner_name
   ,COALESCE(get_json_object(H.additional_data, '$.customerAddress'), get_json_object(H.additional_data, '$.partnerAddress'))  as partner_address
   ,ROUND(CAST(regexp_replace(get_json_object(H.additional_data, '$.gstRate'), ',', '') as double), 2) as gst_rate
   ,ROUND(CAST(regexp_replace(get_json_object(H.additional_data, '$.rate'), ',', '') as double), 4) as rate
   ,CAST(get_json_object(H.additional_data, '$.criteriaDate') as timestamp) as criteria_date
   ,ROUND(CAST(regexp_replace(get_json_object(H.additional_data, '$.totalAmountDue'), ',', '') as double), 2) as total_amount_due
   ,ROUND(CAST(regexp_replace(get_json_object(H.additional_data, '$.totalRevenueAmount'), ',', '') as double) ,2) as total_revenue_amount
   ,COALESCE(get_json_object(H.additional_data, '$.customerEmail'), get_json_object(H.additional_data, '$.partnerEmail'))  as partner_email
   ,CAST(CAST(get_json_object(H.additional_data, '$.periodStart') as bigint) as timestamp) as period_start
   ,ROUND(CAST(regexp_replace(get_json_object(H.additional_data, '$.discountRate'), ',', '') as double), 2) as discount_rate
   ,get_json_object(H.additional_data, '$.title') as title
   ,get_json_object(H.additional_data, '$.invoiceCurrency') as invoice_currency
   ,CAST(CAST(get_json_object(H.additional_data, '$.periodEnd') as bigint) as timestamp) as period_end
       ,T.invoice_transaction_id
       ,T.external_reference_id 
       ,T.transaction_id 
       ,T.booking_id 
       ,CAST(T.booking_date as timestamp) as booking_date
       ,CAST(T.checkin_date as timestamp) as checkin_date
       ,CAST(T.checkout_date as timestamp) as checkout_date
       ,ROUND(CAST(T.amount as double), 2) as transaction_amount
       ,T.hotel_id
       ,T.dmc_id
       ,ROUND(CAST(T.booking_amount as double), 2) as booking_amount
       ,get_json_object(T.additional_data, '$.dataFeedCurrency') as data_feed_currency
       ,ROUND(CAST(regexp_replace(get_json_object(T.additional_data, '$.amountDue'), ',', '') as double), 2) as amount_Due
       ,ROUND(CAST(regexp_replace(get_json_object(T.additional_data, '$.sharedMarginAmountInvoiceCurrency'), ',', '') as double), 2) as        shared_margin_amount_invoice_currency
       ,ROUND(CAST(regexp_replace(get_json_object(T.additional_data, '$.amountDueFunctionalCurrency'), ',', '') as double), 2) as        amount_due_functional_currency
       ,ROUND(CAST(regexp_replace(get_json_object(T.additional_data, '$.sharedMarginAmountDataFeedCurrency'), ',', '') as double), 2) as        shared_margin_amount_data_feed_currency
       ,ROUND(CAST(regexp_replace(get_json_object(T.additional_data, '$.supplierMarginUsd'), ',', '') as double), 2) as invoiced_supplier_margin_usd
       ,ROUND(CAST(regexp_replace(get_json_object(T.additional_data, '$.supplierEssUsd'), ',', '') as double), 2) as invoiced_supplier_ess_usd
       ,ROUND(CAST(regexp_replace(get_json_object(T.additional_data, '$.gstAmountFunctionalCurrency'), ',', '') as double), 2) as        gst_amount_functional_currency
       ,ROUND(CAST(regexp_replace(get_json_object(T.additional_data, '$.amountDueDataFeedCurrency'), ',', '') as double), 2) as        amount_due_data_feed_currency
       ,ROUND(CAST(regexp_replace(get_json_object(T.additional_data, '$.customerPayment'), ',', '') as double), 2) as invoiced_customer_payment
       ,ROUND(CAST(regexp_replace(get_json_object(T.additional_data, '$.revenueAmount'), ',', '') as double), 2) as revenue_amount
       ,ROUND(CAST(regexp_replace(get_json_object(T.additional_data, '$.customerPaymentUsd'), ',', '') as double), 2) as invoiced_customer_payment_usd
       ,ROUND(CAST(regexp_replace(get_json_object(T.additional_data, '$.supplierMargin'), ',', '') as double), 2) as invoiced_supplier_margin_customer
       ,ROUND(CAST(regexp_replace(get_json_object(T.additional_data, '$.supplierEss'), ',', '') as double), 2) as invoiced_supplier_ess_customer
       ,ROUND(CAST(regexp_replace(get_json_object(T.additional_data, '$.revenueAmountInvoiceCurrency'), ',', '') as double), 2) as        revenue_amount_invoice_currency
       ,ROUND(CAST(regexp_replace(get_json_object(T.additional_data, '$.revenueAmountDataFeedCurrency'), ',', '') as double), 2) as        revenue_amount_data_feed_currency
       ,ROUND(CAST(regexp_replace(get_json_object(T.additional_data, '$.gstAmountInvoiceCurrency'), ',', '') as double), 2) as gst_amount_invoice_currency
       ,get_json_object(T.additional_data, '$.trackingTag') as tracking_tag
       ,ROUND(CAST(regexp_replace(get_json_object(T.additional_data, '$.gstAmountDataFeedCurrency'), ',', '') as double), 2) as        gst_amount_data_feed_currency
       ,ROUND(CAST(regexp_replace(get_json_object(T.additional_data, '$.revenueAmountFunctionalCurrency'), ',', '') as double), 2) as        revenue_amount_functional_currency
       ,ROUND(CAST(regexp_replace(get_json_object(T.additional_data, '$.amountDueInvoiceCurrency'), ',', '') as double), 2) as amount_due_invoice_currency
       ,ROUND(CAST(regexp_replace(get_json_object(T.additional_data, '$.sharedMarginAmountFunctionalCurrency'), ',', '') as double), 2) as        shared_margin_amount_functional_currency
       ,ROUND(CAST(regexp_replace(get_json_object(T.additional_data, '$.gstAmount'), ',', '') as double), 2) as gst_amount
       ,CAST(regexp_replace(get_json_object(T.additional_data, '$.discountAmountInvoiceCurrency'), ',', '') as double) as discount_amount_invoice_currency
       ,CAST(regexp_replace(get_json_object(T.additional_data, '$.discountAmountDataFeedCurrency'), ',', '') as double) as discount_amount_data_feed_currency
       ,CAST(regexp_replace(get_json_object(T.additional_data, '$.discountAmountFunctionalCurrency'), ',', '') as double) as discount_amount_functional_currency
       ,CAST(from_unixtime(unix_timestamp(T.booking_date, 'yyyy-MM-dd'), 'yyyyMMdd') as INT) as datadate
FROM finance_common.invoices H
INNER JOIN finance_common.invoice_transactions T on H.invoice_id = T.invoice_id
WHERE H.invoice_method_id = 5
AND H.rec_status = 1
AND  H.datadate = {{datadate}}