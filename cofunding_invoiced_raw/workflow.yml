workflow:
  id: cofunding_invoiced_raw
  name: cofunding_invoiced_raw
  description: Migrate cofunding invoiced raw data from excel to hadoop table
  version: 2
actions:
  - id: upload-from-excel-to-hadoop-fija
    name: upload_from_excel_to_hadoop
    type: h2h
    spec:
      query: upload_from_excel_to_hadoop-fija.query.sql
      targetTable: '{{table_name}}'
      targetDatabase: '{{db_name}}'
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        append: {}
    dependsOn: []
params_profile:
  - name: default
    params:
      - name: db_name
        value: finance_cofunding
        dataType: string
      - name: table_name
        value: migrated_invoiced_raw
        dataType: string
      - name: path
        value: '''hdfs:///user/hk-fin-ar-prod-svc/tmp/UAT/dec_invoiced_bank.csv'''
        dataType: string
      - name: datadate
        value: '********'
        dataType: number
  - name: dev
    params:
      - name: db_name
        value: finance_cofunding_uat
        dataType: string
      - name: path
        value: '''hdfs:///user/hk-fin-ar-prod-svc--dev/tmp/UAT/dec_invoiced_bank.csv'''
        dataType: string
      - name: datadate
        value: '********'
        dataType: number
schedules: []
