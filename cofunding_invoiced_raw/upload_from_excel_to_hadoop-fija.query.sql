CREATE TEMPORARY VIEW init_running_no
USING csv
OPTIONS (
  path {{path}},
  header true,
  delimiter ','
);
SELECT 
CAST(booking_id as bigint) AS booking_id,
CAST(paying_aid_1 as bigint) AS paying_aid_1,
CAST(paying_aid_2 as bigint) AS paying_aid_2,
CAST(cid as int) AS cid,
CAST(booking_date as timestamp) AS booking_date,
CAST(check_in_date as timestamp) AS check_in_date,
CAST(check_out_date as timestamp) AS check_out_date,
CAST(promotion_text as string) AS promotion_text,
CAST(discount_amount  as decimal(18,8)) AS discount_amount,
CAST(partner_1_total_funding  as decimal(18,8)) AS partner_1_total_funding,
CAST(partner_2_total_funding  as decimal(18,8)) AS partner_2_total_funding,
CAST(total_funding  as decimal(18,8)) AS total_funding,  
CAST(book_status as string) AS book_status,
CAST(discount_type as string) AS discount_type,
CAST(campaign_id  as bigint) AS campaign_id,
CAST(campaign_start_date as timestamp) AS campaign_start_date,
CAST(campaign_end_date as timestamp) AS campaign_end_date,
CAST(is_triparty as string) AS is_triparty,
CAST(type as string) AS type,
CAST({{datadate}} as int) AS datadate
FROM init_running_no;