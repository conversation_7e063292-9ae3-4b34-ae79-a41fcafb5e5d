workflow: # please notify team-fintech-wallet if this wf is updated, we may need to make changes on our side
  id: ar-reporter
  name: ar-reporter
  description: ''
  version: 2
actions:
  - name: ar-reporter
    maxRetry: 0
    id: ar-reporter
    dependsOn: []
    spec:
      mainApplicationFile: local:///opt/app/assembly/assembly.jar
      image: >-
        reg-sg.agodadev.io/gitlab/financeplatform/core-platform/finance-reporter/arreporter
      tag: '{{tag}}'
      arguments:
        - date={{datadate}} {{reporter_args}}
      additionalSparkConfigurations:
        - key: spark.executor.java-opts
          value: >-
            -XX:+CMSClassUnloadingEnabled -XX:+HeapDumpOnOutOfMemoryError
            -XX:HeapDumpPath=/agoda/heap_dump/
            -Dhadoop.postgres.connectionString=********************************************************
            -Detl.services.refresh.server-location=PROD_SGP
        - key: spark.serializer
          value: org.apache.spark.serializer.KryoSerializer
        - key: spark.hadoop.hadoop.security.credential.provider.path
          value: ''
        - key: spark.executor.extraJavaOptions
          value: >-
            -XX:+PrintGCDetails -XX:+PrintGCTimeStamps
            -XX:+PrintGCApplicationStoppedTime -XX:+PrintGCCause -verbose:gc
        - key: spark.kerberos.access.hadoopFileSystems
          value: hdfs://nameservice1,hdfs://nameservice2
        - key: spark.eventLog.enabled
          value: 'true'
        - key: spark.executor.memoryOverhead
          value: '8000'
        - key: spark.sql.adaptive.enabled
          value: 'true'
        - key: spark.yarn.populateHadoopClasspath
          value: 'true'
        - key: spark.driver.maxResultSize
          value: 25g
        - key: spark.shuffle.service.enabled
          value: 'true'
        - key: spark.sql.hive.metastore.token.signature
          value: hiveserver2ClientToken
        - key: spark.sql.parquet.int96RebaseModeInRead
          value: LEGACY
        - key: spark.hadoop.hive.execution.engine
          value: mr
        - key: spark.sql.hive.metastore.jars
          value: /usr/hdp/current/hive-client/lib/*:./*
        - key: spark.network.timeout
          value: '600'
        - key: spark.rpc.askTimeout
          value: '600'
        - key: spark.driver.memory
          value: 32G
        - key: spark.executor.instances
          value: '33'
        - key: spark.storage.memoryMapThreshold
          value: '2097152'
        - key: spark.logConf
          value: 'true'
        - key: spark.driver.memoryOverhead
          value: '8000'
        - key: spark.sql.adaptive.coalescePartitions.enabled
          value: 'true'
        - key: spark.sql.legacy.timeParserPolicy
          value: LEGACY
        - key: spark.sql.broadcastTimeout
          value: '3000'
        - key: spark.yarn.maxAppAttempts
          value: '1'
        - key: spark.executor.memory
          value: 24G
        - key: spark.executor.cores
          value: '3'
        - key: spark.sql.parquet.writeLegacyFormat
          value: 'true'
        - key: spark.sql.parquet.int96RebaseModeInWrite
          value: LEGACY
        - key: hive.metastore.disallow.incompatible.col.type.changes
          value: 'false'
        - key: spark.driver.java-opts
          value: >-
            -XX:+CMSClassUnloadingEnabled -XX:+HeapDumpOnOutOfMemoryError
            -XX:HeapDumpPath=/agoda/heap_dump/
            -Dhadoop.postgres.connectionString=********************************************************
            -Detl.services.refresh.server-location=PROD_SGP
        - key: spark.sql.hive.metastore.version
          value: 3.1.1
        - key: spark.yarn.appMasterEnv.OTEL_SERVICE_NAME
          value: finance-reporter
        - key: spark.yarn.appMasterEnv.OTEL_TRACES_SAMPLER
          value: always_on
        - key: spark.executorEnv.OTEL_SERVICE_NAME
          value: finance-reporter
        - key: spark.yarn.appMasterEnv.AG_DC
          value: sg
        - key: spark.yarn.appMasterEnv.AG_SERVICE_NAME
          value: finance-reporter
        - key: spark.executorEnv.AG_DC
          value: sg
        - key: spark.executorEnv.AG_SERVICE_NAME
          value: finance-reporter
      sparkVersion: 3.3.1
      mainClass: com.agoda.finance.Main
      driver:
        memoryOverhead: 8000m
        javaOptions:
          - '-XX:+CMSClassUnloadingEnabled'
          - '-XX:+HeapDumpOnOutOfMemoryError'
          - '-XX:HeapDumpPath=/agoda/heap_dump/'
          - >-
            -Dhive.metastore.filter.hook=com.agoda.adp.hadoop.hive.client.hook.AdpFilterHook
          - '-Dconfig.strategy=com.agoda.ml.config.MultiResourceStrategy'
          - '-Dconfig.resource={{config}}'
          - '-Dlog4j.configuration={{log4j_config}}'
          - '-Dhadoop.spark.submituser={{submit_user}}'
        memory: 40g
      dynamicAllocation: {}
      executor:
        instances: '33'
        cores: '3'
        memory: 24g
        memoryOverhead: 8000m
        javaOptions:
          - '-XX:+CMSClassUnloadingEnabled'
          - '-XX:+HeapDumpOnOutOfMemoryError'
          - '-XX:HeapDumpPath=/agoda/heap_dump/'
          - >-
            -Dhive.metastore.filter.hook=com.agoda.adp.hadoop.hive.client.hook.AdpFilterHook
          - '-Dconfig.strategy=com.agoda.ml.config.MultiResourceStrategy'
          - '-Dconfig.resource={{config}}'
          - '-Dlog4j.configuration={{log4j_config}}'
          - '-Dhadoop.spark.submituser={{submit_user}}'
      dependencies:
        jars:
          - local:///opt/app/assembly/dependency_cache.jar
    type: spark
    retryInterval: 1
schedules: []
params_profile:
  - name: default
    params:
      - name: datadate
        value: __deferred
        dataType: number
      - name: reporter_args
        value: __deferred
        dataType: string
      - name: tag
        value: __deferred
        dataType: string
      - name: config
        value: __deferred
        dataType: string
      - name: log4j_config
        value: log4j_adp.properties
        dataType: string
      - name: submit_user
        value: hk-fin-ar-prod-svc
        dataType: string
  - name: dev
    params:
      - name: log4j_config
        value: log/log4j_adp_qa.properties
        dataType: string
      - name: submit_user
        value: hk-fin-ar-prod-svc--dev
        dataType: string
