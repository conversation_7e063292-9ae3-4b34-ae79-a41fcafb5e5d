workflow:
  id: avl_daily_assetliab_oc_currency
  name: avl_daily_assetliab_oc_currency
  description: ''
  version: 2
actions:
  - id: backup-table-bila
    name: backup table
    type: h2h
    spec:
      query: backup-table-bila.query.sql
      targetTable: daily_assetliab_oc_currency_backup
      targetDatabase: finance_avl
      partitionBy:
        staticValue:
          columns:
            - name: master
              type: string
              value: master
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn: []
  - id: add-new-currency-fefk
    name: add new currency
    type: h2h
    spec:
      query: add-new-currency-fefk.query.sql
      targetTable: '{{resolve_table_name("daily_assetliab_oc_currency")}}'
      targetDatabase: finance_avl
      partitionBy:
        staticValue:
          columns:
            - name: master
              type: string
              value: master
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - backup-table-bila
params_profile:
  - name: default
    params: []
  - name: dev
    params: []
schedules: []
