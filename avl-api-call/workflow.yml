workflow:
  id: avl-api-call
  name: AVL API Call
  description: >-
    Workflow to call API endpoint in fintech-service to trigger avl report
    generation.
  version: 2
actions:
  - id: new-shell-action-cba5
    name: Call fintech service AVL API
    type: shell
    spec:
      commandFile: hdfs:///user/hk-fin-ar-prod-svc/avl/call_fintech_service_avl_api.sh
      captureOutput: false
      files:
        - hdfs:///user/hk-fin-ar-prod-svc/avl/call_fintech_service_avl_api.sh
    dependsOn: []
params_profile:
  - name: default
    params: []
  - name: dev
    params: []
schedules:
  - id: avl-api-coordinator-15-min-d3b7
    name: 'AVL API Coordinator 15 Min '
    timezone: UTC
    cron: 15-45/15 3-6 * * *
    parameters: []
    execution: FIFO
  - id: avl-api-call-coordinator-1-min-0e4e
    name: AVL API Call Coordinator 1 Min
    timezone: UTC
    cron: 1 4-7 * * *
    parameters: []
    execution: FIFO
