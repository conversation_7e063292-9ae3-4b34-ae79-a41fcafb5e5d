workflow:
  id: cofunding_h2h_invoiced_transactions
  name: Cofunding_H2H_invoiced_transactions
  description: ''
  version: 2
actions:
  - id: h2h-invoice-dcke
    name: H2H invoiced by invoice_date
    type: h2h
    spec:
      query: h2h-invoice-dcke.query.sql
      targetTable: invoiced_transactions_by_invoice_date
      targetDatabase: '{{target_database}}'
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      enableValidationRule: true
      trackingColumns:
        - datadate
      mergeBy:
        columnBased:
          keyColumns:
            - invoice_transaction_id
          checkDuplicate: true
      sparkSetting: {}
      alterTableSchema: true
      additionalSparkConfigurations:
        - key: spark.sql.parquet.writeLegacyFormat
          value: 'true'
        - key: spark.sql.legacy.timeParserPolicy
          value: LEGACY
      keepOldVersionsNewerThan: 8
      deleteOldVersionsOlderThan: 192
    dependsOn: []
    maxRetry: '3'
    retryInterval: '10'
params_profile:
  - name: default
    params:
      - name: datadate
        value: __deferred
        dataType: date
      - name: target_database
        value: finance_cofunding
        dataType: string
      - name: finance_common_db
        value: finance_common
        dataType: string
      - name: invoices_table_name
        value: invoices
        dataType: string
      - name: invoice_transactions_table_name
        value: invoice_transactions
        dataType: string
  - name: dev
    params:
      - name: target_database
        value: finance_cofunding_uat
        dataType: string
      - name: finance_common_db
        value: finance_common_qa
        dataType: string
      - name: invoices_table_name
        value: invoices_sirin
        dataType: string
      - name: invoice_transactions_table_name
        value: invoice_transactions_sirin
        dataType: string
schedules:
  - id: h2h_cofunding_invoiced_transaction-caaf
    name: H2H_cofunding_invoiced_transaction
    timezone: UTC
    cron: 0 18 * * *
    parameters:
      - dataType: date
        name: datadate
        value:
          dateFormat: yyyyMMdd
          offsets:
            - offset: 0
              format: Hour
    execution: FIFO
