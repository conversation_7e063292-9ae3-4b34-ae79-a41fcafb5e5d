SELECT
    H.invoice_id 
   ,H.invoice_method_id
   ,H.reference_number
   ,H.payer_source_id
   ,CAST(H.invoice_date as timestamp) as invoice_date
   ,CAST(H.period_from as timestamp) as period_from
   ,CAST(H.period_to as timestamp) as period_to
   ,H.currency_code 
   ,ROUND(CAST(H.amount as double), 2) as header_amount
   ,ROUND(CAST(H.net_amount as double), 2) as net_amount
   ,H.invoice_status_id 
   ,H.document_id 
   ,H.rec_status 
   ,CAST(H.rec_created_when as timestamp) as rec_created_when 
   ,H.rec_created_by 
   ,CAST(H.rec_modified_when as timestamp) as rec_modified_when
   ,H.rec_modify_by
    ,CAST(NULL as int) as affiliate_site_id
    ,CAST(regexp_replace(get_json_object(H.additional_data, '$.creditTerm'), ',', '') as int) as credit_term
    ,ROUND(CAST(regexp_replace(get_json_object(H.additional_data, '$.totalGstAmount'), ',', '') as double), 2) as total_gst_amount
    ,get_json_object(H.additional_data, '$.functionalCurrency') as functional_currency
    ,get_json_object(H.additional_data, '$.partnerName') as partner_name
    ,get_json_object(H.additional_data, '$.partnerAddress') as partner_address
    ,ROUND(CAST(regexp_replace(get_json_object(H.additional_data, '$.gstRate'), ',', '') as double), 2) as gst_rate
    ,get_json_object(H.additional_data, '$.criteriaDate') as criteria_date
    ,get_json_object(H.additional_data, '$.partnerEmail') as partner_email
    ,CAST(get_json_object(H.additional_data, '$.campaignStart') as timestamp) as campaign_start
    ,ROUND(CAST(regexp_replace(get_json_object(H.additional_data, '$.discountRate'), ',', '') as double), 2) as discount_rate
    ,get_json_object(H.additional_data, '$.title') as title
    ,get_json_object(H.additional_data, '$.invoiceCurrency') as invoice_currency
    ,CAST(get_json_object(H.additional_data, '$.campaignEnd') as timestamp) as campaign_end
    ,CAST(regexp_replace(get_json_object(H.additional_data, '$.promotionCampaignId'), ',', '') as int) as promotion_campaign_id
    ,get_json_object(H.additional_data, '$.description') as description
    ,ROUND(CAST(regexp_replace(get_json_object(H.additional_data, '$.subtotalPartnerFundingAmountSGD'), ',', '') as double), 2) as sub_total_partner_funding_amount_SGD
    ,ROUND(CAST(regexp_replace(get_json_object(H.additional_data, '$.totalPartnerFundingAmountSGD'), ',', '') as double), 2) as total_partner_funding_amount_SGD
    ,ROUND(CAST(regexp_replace(get_json_object(H.additional_data, '$.exchangeRateUsed'), ',', '') as double), 6) as exchange_rate_used
    ,ROUND(CAST(regexp_replace(get_json_object(H.additional_data, '$.subtotalPartnerFundingAmountUSD'), ',', '') as double), 2) as sub_total_partner_funding_amount_USD
    ,ROUND(CAST(regexp_replace(get_json_object(H.additional_data, '$.taxPartnerFundingAmountSGD'), ',', '') as double), 2) as tax_partner_funding_amount_SGD
    ,ROUND(CAST(regexp_replace(get_json_object(H.additional_data, '$.totalPartnerFundingAmountUSD'), ',', '') as double), 2) as total_partner_funding_amount_USD
    ,ROUND(CAST(regexp_replace(get_json_object(H.additional_data, '$.totalPartnerFundingAmountInvoiceCurrency'), ',', '') as double), 2) as total_partner_funding_amount_invoice_currency
   ,T.invoice_transaction_id
   ,CAST(T.booking_id as bigint) as booking_id
   ,CAST(T.booking_date as timestamp) as booking_date
   ,CAST(T.checkin_date as timestamp) as checkin_date
   ,CAST(T.checkout_date as timestamp) as checkout_date
   ,ROUND(CAST(T.amount as double), 2) as transaction_amount
   ,T.hotel_id
   ,ROUND(CAST(T.booking_amount as double), 2) as booking_amount
   ,CAST('USD' as string) as cofunding_currency
   ,CAST(NULL as string) as invoiced_currency
   ,ROUND(CAST(T.amount as double), 2) as invoice_amount
   ,ROUND(CAST(T.amount as double), 2) as cofunding_amount
   ,CAST(NULL as double) as gst_amount_usd
   ,CAST(from_unixtime(unix_timestamp(H.invoice_date, 'yyyy-MM-dd'), 'yyyyMMdd') as INT) as datadate
FROM {{finance_common_db}}.{{invoices_table_name}} H
INNER JOIN {{finance_common_db}}.{{invoice_transactions_table_name}} T on H.invoice_id = T.invoice_id
WHERE H.invoice_method_id = 42
AND H.rec_status = 1
AND  H.datadate = {{datadate}}
--refresh schedule