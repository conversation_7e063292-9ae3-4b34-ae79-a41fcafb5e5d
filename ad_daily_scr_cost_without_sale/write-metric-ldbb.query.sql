SELECT scr.product_type                                                                AS product_type
     , COUNT(DISTINCT scr.booking_id)                                                  AS all_booking_count
     , SUM(COALESCE(cost, 0))                                                          AS all_cost_amount
     , COUNT(DISTINCT CASE WHEN scr.sale = 0 AND scr.cost > 0 THEN scr.booking_id END) AS is_cost_only_booking_count
     , SUM(CASE WHEN scr.sale = 0 AND scr.cost > 0 THEN cost ELSE 0 END)               AS is_cost_only_amount
     , COUNT(DISTINCT CASE WHEN scr.sale = 0 AND scr.cost > 0 THEN scr.booking_id END) /
       COUNT(DISTINCT scr.booking_id)                                                  AS is_cost_only_vs_all_booking_count_ratio
     , SUM(CASE WHEN scr.sale = 0 AND scr.cost > 0 THEN cost ELSE 0 END) /
       SUM(COALESCE(cost, 0))                                                          AS is_cost_only_vs_all_amount_ratio
     , {{hive_yesterday_datadate}}                                                     AS datadate
FROM (SELECT booking_id
           , product_type
           , SUM(CASE
                     WHEN source = 'SALE' THEN usd_defer_amount
                     ELSE 0 END) AS sale
           , SUM(CASE
                     WHEN source = 'COST' THEN usd_amount_exc_gst
                     ELSE 0 END) AS cost
           , MIN(start_date)     AS start_date
      FROM finance_multiproduct_scr_daily.sale_cost_recognition
      WHERE booking_id IN (SELECT booking_id
                           FROM finance_multiproduct_scr_daily.sale_cost_recognition scr
                           LEFT ANTI JOIN bi_ceg.fact_absorption_booking fab on scr.booking_id = fab.booking_id AND fab.actual_absorption != 0 -- Exclude CEG absorption
                           WHERE datadate = {{hive_yesterday_datadate}}
                             AND status = 'RECOGNIZED'
                             AND affiliate_model <> 'B2B'
                             AND payment_model <> 2 -- Exclude B2B and Pay At Hotel
                             AND source = 'COST'
                             AND booking_id IS NOT NULL)
        AND status = 'RECOGNIZED'
      GROUP BY booking_id, product_type) scr
         LEFT JOIN finance_dw.booking_info bi
                   ON scr.booking_id = bi.booking_id AND scr.product_type = bi.product_type
WHERE ((scr.product_type = 'HOTEL'
    AND bi.charge_option_id = 1
    AND bi.delay_settlement_date IS NULL)
    OR
       scr.product_type != 'HOTEL') -- Exclude BNPL and Delay Settlement for HOTEL product
GROUP BY scr.product_type -- Only product types with cost
HAVING COUNT (DISTINCT scr.booking_id) > 0 AND SUM (COALESCE (scr.cost, 0)) > 0; 
