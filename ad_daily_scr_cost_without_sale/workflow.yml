workflow:
  id: ad_daily_scr_cost_without_sale
  name: ad_daily_scr_cost_without_sale
  description: ''
  version: 2
actions:
  - id: write-metric-ldbb
    name: write_metric
    type: h2h
    spec:
      query: write-metric-ldbb.query.sql
      targetTable: '{{resolve_table_name("daily_scr_cost_without_sale")}}'
      targetDatabase: finance_metrics
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: source
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn: []
params_profile:
  - name: default
    params:
      - name: hive_yesterday_datadate
        value: __deferred
        dataType: date
  - name: dev
    params: []
schedules: []
