CREATE TEMPORARY VIEW init_running_no
USING csv
OPTIONS (
  path 'hdfs:///user/hk-fin-ar-prod-svc--dev/tmp/ProdCofunding/live_bank1.csv',
  header true,
  delimiter ','
);
SELECT 
CAST(booking_id as bigint)  AS booking_id,
CAST(paying_aid1 as int) AS paying_aid1,
CAST(paying_aid2 as int) AS paying_aid2,
CAST(total_funding as decimal(18,8)) AS total_funding,
CAST(book_status as string) AS book_status,
CAST(discount_type as string) AS discount_type,
CAST(campaign_id as int) AS campaign_id,
CAST(campaign_start_date as timestamp) AS campaign_start_date,
CAST(campaign_end_date as timestamp) AS campaign_end_date,
CAST(type as string) AS type
FROM init_running_no;