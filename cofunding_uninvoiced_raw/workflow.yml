workflow:
  id: cofunding_uninvoiced_raw
  name: cofunding_uninvoiced_raw
  description: Raw uninvoiced cofunding data
  version: 2
actions:
  - id: bank-1-fcci
    name: bank_1
    type: h2h
    spec:
      query: bank_1-fcci.query.sql
      targetTable: migrated_uninvoiced_raw
      targetDatabase: '{{finance_cofunding_db}}'
      partitionBy:
        columnBased:
          columns:
            - name: type
              type: string
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn: []
  - id: bank-2-dkfj
    name: bank_2
    type: h2h
    spec:
      query: bank_2-dkfj.query.sql
      targetTable: migrated_uninvoiced_raw
      targetDatabase: '{{finance_cofunding_db}}'
      partitionBy:
        columnBased:
          columns:
            - name: type
              type: string
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - bank-1-fcci
  - id: mc-kjkf
    name: MC
    type: h2h
    spec:
      query: mc-kjkf.query.sql
      targetTable: migrated_uninvoiced_raw
      targetDatabase: '{{finance_cofunding_db}}'
      partitionBy:
        columnBased:
          columns:
            - name: type
              type: string
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - bank-2-dkfj
  - id: migrate-to-invalid-table-idlb
    name: migrate to invalid table
    type: h2h
    spec:
      query: migrate-to-invalid-table-idlb.query.sql
      targetTable: invalid_daily_transactions
      targetDatabase: '{{finance_cofunding_db}}'
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - mc-kjkf
params_profile:
  - name: default
    params:
      - name: finance_cofunding_db
        value: finance_cofunding
        dataType: string
      - name: datadate
        value: __deferred
        dataType: number
  - name: dev
    params:
      - name: finance_cofunding_db
        value: finance_cofunding_uat
        dataType: string
schedules: []
