WITH ft_ranked_datadate AS (
    SELECT 
        *
        , ROW_NUMBER() OVER (PARTITION BY booking_id ORDER BY datadate DESC) AS row_num
    FROM 
        finance_multiproduct.financial_transactions
    WHERE source_type LIKE '%BREAKDOWN%'
          and datadate <= 20250331
          and booking_id in (select booking_id from finance_cofunding.migrated_uninvoiced_raw)
)
SELECT 
     FT.booking_id,
     itinerary_payment_id,
     transaction_id,
     transaction_type,
     reference_type,
     charge_date,
     transaction_date,
     cid,
     booking_date,
     start_date,
     end_date,
     cancellation_date,
     sub_supplier_id,
     supplier_id,
     service_origin_iso3,
     merchant_of_record_entity,
     revenue_entity,
     rate_contract_entity,
     customer_origin_iso2,
     is_test,
     customer_payment_currency,
     supplier_currency,
     fx_customer_payment_rate,
     fx_supplier_rate,
     supplier_name,
     sub_supplier_name,
     mor_name,
     rev_name,
     rce_name,
     booking_month,
     created_log_time,
     source_type,
     booking_datadate_line_number,
     source_datadate,
     uuid,
     adjustment_identifier,
     promotion_amount,
     promotion_amount_usd,
     promotion_amount_customer,
     adjustment_type,
     co_funding_amount,
     MUR.total_funding as co_funding_amount_usd,
     co_funding_amount_customer,
     MUR.campaign_id as promotion_campaign_id,
     MUR.discount_type as discount_type,
     MUR.type as campaign_type,
     product_type,
     affiliate_model,
     '(UAT)migrate to uninvoiced' as invalid_reason,
     CAST({{datadate}} as int) AS datadate
FROM 
     ft_ranked_datadate FT
JOIN finance_cofunding.migrated_uninvoiced_raw MUR on FT.booking_id = MUR.booking_id
WHERE 
    row_num = 1