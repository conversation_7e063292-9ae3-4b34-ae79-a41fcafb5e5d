select 
affiliate_site_id
,CAST(booking_amount as double) AS booking_amount
,booking_date
,booking_id
,campaign_end
,campaign_start
,checkin_date
,checkout_date
,CAST(cofunding_amount as double) AS cofunding_amount
,cofunding_currency
,credit_term
,criteria_date
,currency_code
,datadate
,description
,CAST(discount_rate as double) AS discount_rate
,document_id
,functional_currency
,CAST(gst_amount_usd as double) AS gst_amount_usd
,CAST(gst_rate as double) AS gst_rate
,CAST(header_amount as double) AS header_amount
,hotel_id
,CAST(invoice_amount as double) AS invoice_amount
,invoice_currency
,invoice_date
,CAST(invoice_id as int) AS invoice_id
,invoice_method_id
,invoice_status_id
,invoice_transaction_id
,invoiced_currency
,CAST(net_amount as double) AS net_amount
,partner_address
,partner_email
,partner_name
,payer_source_id
,period_from
,period_to
,promotion_campaign_id
,rec_created_by
,rec_created_when
,rec_modified_when
,rec_modify_by
,rec_status
,reference_number
,CAST(sub_total_partner_funding_amount_sgd as double) AS sub_total_partner_funding_amount_sgd
,title
,CAST(total_gst_amount as double) AS total_gst_amount
,CAST(total_partner_funding_amount_sgd as double) AS total_partner_funding_amount_sgd
,CAST(transaction_amount as double) AS transaction_amount
,CAST(0 as double) as exchange_rate_used
,CAST(0 as double) as sub_total_partner_funding_amount_usd
,CAST(0 as double) as tax_partner_funding_amount_sgd 
,CAST(0 as double) as total_partner_funding_amount_invoice_currency
,CAST(0 as double) as total_partner_funding_amount_usd
from finance_cofunding.invoiced_transactions_by_invoice_date_backup
