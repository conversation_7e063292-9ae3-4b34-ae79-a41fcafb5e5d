workflow:
  id: cofunding_move_backup_data
  name: cofunding_move_backup_data
  description: ''
  version: 2
actions:
  - id: migrate-hkja
    name: migrate
    type: h2h
    spec:
      query: migrate-hkja.query.sql
      targetTable: invoiced_transactions_by_invoice_date
      targetDatabase: finance_cofunding
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn: []
params_profile:
  - name: default
    params: []
  - name: dev
    params: []
schedules: []
