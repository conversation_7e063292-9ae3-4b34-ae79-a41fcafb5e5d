workflow:
  id: cofunding
  name: cofunding
  description: ''
  version: 2
actions:
  - id: invoice-data-processor-cggl
    name: invoice-data-processor
    type: sub-workflow
    spec:
      workflowPath: >-
        git://etl-workflows/wf-service-accounts/hk-fin-ar-prod-svc/cofunding-invoice-data-processor#main
      subWorkflowProperties:
        - key: datadate
          value: '{{hive_today_date}}'
        - key: processor_args
          value: '{{processor_args}}'
        - key: tag
          value: '{{processor_tag}}'
        - key: config
          value: '{{processor_config}}'
        - key: log4j_config
          value: '{{processor_log4j_config}}'
        - key: submit_user
          value: '{{submit_user}}'
      propagateConfigurations: true
    dependsOn: []
  - id: upload-daily-report-cofunding-khed
    name: upload-daily-report-cofunding
    type: shell
    spec:
      commandFile: hdfs:///user/hk-fin-ar-prod-svc/shell_script/erpUpload.sh
      captureOutput: false
      arguments:
        - '{{is_upload_to_erp}}'
        - '{{erp_connector_server}}'
        - '{{report_upload_date}}'
        - '222'
      files:
        - hdfs:///user/hk-fin-ar-prod-svc/shell_script/erpUpload.sh
    dependsOn:
      - join-ibch
  - id: uninvoiced-casper-gfie
    name: uninvoiced_casper
    type: sub-workflow
    spec:
      workflowPath: >-
        git://etl-workflows/wf-service-accounts/hk-fin-ar-prod-svc/uninvoiced_transactions_casper#main
      subWorkflowProperties:
        - key: db_name
          value: '{{db_name}}'
        - key: hive_today_date
          value: '{{hive_today_date}}'
      propagateConfigurations: true
    dependsOn:
      - fork-clgd
  - id: daily-report-cofunding-etl-ejca
    name: daily-report-cofunding-etl
    type: sub-workflow
    spec:
      workflowPath: >-
        git://etl-workflows/wf-service-accounts/hk-fin-ar-prod-svc/daily-booking-cofunding-report-wf#main
      subWorkflowProperties:
        - key: db_name
          value: finance_multiproduct_ar_reports
        - key: destination_file_name
          value: DailyBooking_AR_Cofunding_ACS
        - key: report_name
          value: cofunding_acs
        - key: publish_to_sftp
          value: 'true'
        - key: publish_to_vast
          value: 'true'
        - key: report_format_name
          value: oracle
        - key: reporter_report_table
          value: report_table
        - key: sftp_path
          value: /upload/multi-product/ERPInbound_Live/ar/DailyBooking_AR_Cofunding
        - key: spark_queue
          value: production
        - key: src_finance_table
          value: finance_cofunding.valid_daily_transactions
        - key: table_classification
          value: classified_table
        - key: vast_path
          value: >-
            s3a://hk-fin-ar-prod-svc/reports/multi-product/erp/DailyBooking_AR_Cofunding
        - key: view_aggregation
          value: aggregated_view
        - key: view_classification
          value: classified_view
        - key: classification_val_exec_flow_id
          value: '103293'
        - key: report_table_val_exec_flow_id
          value: '103294'
        - key: as_of_date
          value: '{{hive_today_date}}'
      propagateConfigurations: true
    dependsOn:
      - fork-clgd
  - id: fork-clgd
    name: fork-clgd
    type: fork
    dependsOn:
      - invoice-data-processor-cggl
  - id: join-ibch
    name: join-ibch
    type: join
    dependsOn:
      - uninvoiced-casper-gfie
      - daily-report-cofunding-etl-ejca
      - uninvoiced-status-count-fjch
  - id: uninvoiced-status-count-fjch
    name: uninvoiced-status-count
    type: h2h
    spec:
      query: uninvoiced-status-count-fjch.query.sql
      targetTable: '{{resolve_table_name("uninvoiced_status")}}'
      targetDatabase: '{{status_db_name}}'
      partitionBy:
        columnBased:
          columns:
            - name: invoice_type
              type: source
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - fork-clgd
params_profile:
  - name: default
    params:
      - name: hive_today_date
        value: __deferred
        dataType: date
      - name: processor_args
        value: slack=false
        dataType: string
      - name: processor_tag
        value: release
        dataType: string
      - name: processor_config
        value: prod.conf
        dataType: string
      - name: processor_log4j_config
        value: log4j_adp.properties
        dataType: string
      - name: submit_user
        value: hk-fin-ar-prod-svc
        dataType: string
      - name: reporter_multiproduct_daily_cofunding_config
        value: sg/release_multiproduct_daily_booking_cofunding.conf
        dataType: string
      - name: is_upload_to_erp
        value: 'true'
        dataType: string
      - name: erp_connector_server
        value: prod
        dataType: string
      - name: report_upload_date
        value: __deferred
        dataType: date
      - name: reporter_tag
        value: release
        dataType: string
      - name: db_name
        value: finance_cofunding
        dataType: string
      - name: reporter_args
        value: slack=true
        dataType: string
      - name: status_db_name
        value: finance_invoice
        dataType: string
      - name: status_check_needed
        value: 'true'
        dataType: string
  - name: dev
    params:
      - name: processor_tag
        value: __deferred
        dataType: string
      - name: processor_config
        value: staging.conf
        dataType: string
      - name: submit_user
        value: hk-fin-ar-prod-svc--dev
        dataType: string
      - name: reporter_multiproduct_daily_cofunding_config
        value: sg/staging_multiproduct_daily_booking_cofunding.conf
        dataType: string
      - name: erp_connector_server
        value: dev
        dataType: string
      - name: db_name
        value: finance_cofunding_uat
        dataType: string
      - name: status_db_name
        value: finance_invoice_qa
        dataType: string
schedules:
  - id: daily-schedule-gfkg
    name: daily-schedule
    timezone: UTC
    cron: 30 22 * * *
    parameters:
      - dataType: date
        name: hive_today_date
        value:
          dateFormat: yyyyMMdd
          offsets:
            - offset: 7
              format: Hour
      - dataType: date
        name: report_upload_date
        value:
          dateFormat: yyyy-MM-dd
          offsets:
            - offset: 7
              format: Hour
    execution: FIFO
    dataDependencies:
      - table: finance_multiproduct.financial_transactions
        column: daily_datetime
        method: '>='
        offset: -1
        format: Day
        granularity: Minute
