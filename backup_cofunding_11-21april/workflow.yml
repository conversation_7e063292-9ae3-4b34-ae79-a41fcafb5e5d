workflow:
  id: backup_cofunding_11-21april
  name: backup_cofunding_11-21April
  description: ''
  version: 2
actions:
  - id: valid-lgbf
    name: valid
    type: h2h
    spec:
      query: valid-lgbf.query.sql
      targetTable: valid_daily_transactions_1_21_april
      targetDatabase: finance_cofunding_uat
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn: []
  - id: invalid-ckcl
    name: invalid
    type: h2h
    spec:
      query: invalid-ckcl.query.sql
      targetTable: invalid_daily_transactions_1_21_april
      targetDatabase: finance_cofunding_uat
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - valid-lgbf
  - id: excluded-ekhl
    name: excluded
    type: h2h
    spec:
      query: excluded-ekhl.query.sql
      targetTable: excluded_daily_transactions_1_21_april
      targetDatabase: finance_cofunding_uat
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - invalid-ckcl
  - id: uninvoiced-bfjk
    name: uninvoiced
    type: h2h
    spec:
      query: uninvoiced-bfjk.query.sql
      targetTable: uninvoiced_transactions_1_21_april
      targetDatabase: finance_cofunding_uat
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - excluded-ekhl
params_profile:
  - name: default
    params: []
  - name: dev
    params: []
schedules: []
