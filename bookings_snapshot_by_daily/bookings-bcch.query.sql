SELECT booking_id
,itinerary_id
,cid
,cid_name
,payment_model
,payment_model_name
,confirmation_date
,booking_date
,start_date
,end_date
,cancellation_date
,commission_model_cutoff_date
,tracking_tag
,supplier_id
,supplier_name
,supplier_booking_id
,sub_supplier_id
,sub_supplier_name
,supplier_status_code
,service_origin_id
,service_origin_iso2
,service_origin_iso3
,merchant_of_record_entity
,merchant_of_record_entity_name
,revenue_entity
,revenue_entity_name
,rate_contract_entity
,rate_contract_entity_name
,merchant_of_record_entity_type
,merchant_of_record_entity_type_name
,revenue_entity_type
,revenue_entity_type_name
,rate_contract_entity_type_name
,rate_contract_entity_type
,whitelabel_id
,whitelabel_name
,customer_origin_iso2
,service_provider
,service_provider_reference
,void_window_until
,is_test
,is_advance_guarantee
,cancellation_policy_code
,row_num
,log_date
,booking_month
,created_log_time
,updated_log_time
,whitelabel_group_id
,workflow_phase_id
,charge_option_id
,sell_tag_id
,original_bnpl_fully_charge_date
,original_bnpl_fully_auth_date
,resell_booking_id
,contract_type_id
,rate_channel
,affliate_id as affiliate_id
,resell_status
,member_id
,customer_iso2
,customer_iso2_currency
,resell_type_id
,resell_status_id
,program_name
,customer_iso2_id
,room_nights
,product_type
,affiliate_model
,cast(date_format(current_date(), "yyyyMMdd") AS INT) as datadate
FROM finance_multiproduct.bookings