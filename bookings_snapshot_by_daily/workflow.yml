workflow:
  id: bookings_snapshot_by_daily
  name: bookings_snapshot_by_daily
  description: ''
  version: 2
actions:
  - id: bookings-bcch
    name: bookings
    type: h2h
    spec:
      query: bookings-bcch.query.sql
      targetTable: bookings_snapshot_backup
      targetDatabase: '{{finance_multiproduct_scr_daily_db}}'
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
      alterTableSchema: true
      maxDuration: 900
      deleteOldVersionsOlderThan: 192
      keepOldVersionsNewerThan: 8
      additionalSparkConfigurations: []
    dependsOn: []
    maxRetry: '3'
    retryInterval: '10'
  - id: old-bookings-dlij
    name: old_bookings
    type: h2h
    spec:
      query: old_bookings-dlij.query.sql
      targetTable: bookings_snapshot_backup
      targetDatabase: '{{finance_multiproduct_scr_daily_db}}'
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        append: {}
      alterTableSchema: true
      maxDuration: 900
      deleteOldVersionsOlderThan: 192
      keepOldVersionsNewerThan: 8
      additionalSparkConfigurations: []
    dependsOn:
      - bookings-bcch
    maxRetry: '3'
    retryInterval: '10'
params_profile:
  - name: default
    params:
      - name: finance_multiproduct_scr_daily_db
        value: finance_multiproduct_scr_daily
        dataType: string
  - name: dev
    params:
      - name: finance_multiproduct_scr_daily_db
        value: finance_multiproduct_scr_daily_dev
        dataType: string
schedules:
  - id: booking_snapshot_by_daily-ebhi
    name: booking_snapshot_by_daily
    timezone: UTC
    cron: 0 2 * * *
    parameters: []
