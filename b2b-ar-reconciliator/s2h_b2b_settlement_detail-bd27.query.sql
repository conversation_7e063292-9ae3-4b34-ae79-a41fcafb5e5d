SELECT		D.b2b_settlement_detail_id
			, D.b2b_settlement_id
			, S.file_name
			, D.booking_id
			, D.statement_no
			, convert(varchar(10),D.settlement_date, 120) + ' 00:00:00' as settlement_date
			, D.currency_code
			, D.amount
			, D.b2b_settlement_type_id
			, E.b2b_settlement_type_name  
			, D.cid
			, D.itinerary_id
			, convert(varchar(19),D.booking_date, 120) as booking_date
			, convert(varchar(19),D.checkin_date, 120) as checkin_date
			, convert(varchar(19),D.checkout_date, 120) as checkout_date
			, D.merchant_of_record
			, D.revenue
			, D.rate_contract
			, D.whitelabel_id
			, D.tracking_tag
			, D.booking_cid
			, D.gateway_id
			, convert(varchar(10),D.invoice_date, 120) + ' 00:00:00' as invoice_date
			, D.credit_term
			, D.created_by
			, D.created_when
			, D.modified_by
			, D.modified_when
			, {{hive_today_date}} AS datadate
FROM		agoda_finance.dbo.b2b_settlement_detail D
INNER JOIN	agoda_finance.dbo.b2b_settlement S
	ON		D.b2b_settlement_id  = S.b2b_settlement_id
LEFT JOIN	agoda_finance.dbo.enum_b2b_settlement_type E
	ON		D.b2b_settlement_type_id = E.b2b_settlement_type_id
WHERE D.created_when >= '{{from}}' AND D.created_when < '{{to}}'
AND $CONDITIONS