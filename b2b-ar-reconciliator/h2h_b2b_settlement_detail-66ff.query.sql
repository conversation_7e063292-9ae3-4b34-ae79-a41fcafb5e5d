SELECT		b2b_settlement_detail_id
			, b2b_settlement_id
			, file_name
			, booking_id
			, statement_no
			, cast(settlement_date as timestamp) as settlement_date
			, currency_code
			, CAST(amount as double) as amount
			, b2b_settlement_type_id
			, b2b_settlement_type_name  
			, cid
			, itinerary_id
			, cast(booking_date as timestamp) as booking_date
			, cast(checkin_date as timestamp) as checkin_date
			, cast(checkout_date as timestamp) as checkout_date
			, merchant_of_record
			, revenue
			, rate_contract
			, whitelabel_id
			, tracking_tag
			, booking_cid
			, gateway_id
			, cast(invoice_date as timestamp) as invoice_date
			, credit_term
			, created_by
			,  cast(from_unixtime(created_when / 1000,'yyyy-MM-dd HH:mm:ss') AS timestamp) AS created_when
			, modified_by
			, cast(from_unixtime(modified_when / 1000,'yyyy-MM-dd HH:mm:ss') AS timestamp) AS modified_when
			, datadate
FROM		{{finance_b2b_db}}.b2b_settlement_detail_raw AS RAW
WHERE		datadate = {{hive_today_date}}
AND			RAW.b2b_settlement_detail_id NOT IN	
			(	SELECT DISTINCT b2b_settlement_detail_id 
				FROM {{finance_b2b_db}}.b2b_settlement_detail
				WHERE datadate >= CAST(DATE_FORMAT(ADD_MONTHS(CURRENT_DATE(), -1),'yyyyMMdd') AS INT)
			)  -- check duplicate back to 1 month