workflow:
  id: b2b-ar-reconciliator
  name: b2b-ar-reconciliator-and-report
  description: "migrated from \nhttps://production-sgprod-hadoop.agoda.local:8888/oozie/editor/workflow/edit/?workflow=f51df36d-8355-4b16-b19e-d5431025467d"
  version: 2
actions:
  - id: fork-5c5f
    name: fork-5c5f
    type: fork
    dependsOn: []
  - id: s2h-b2b-settlement-detail-bd27
    name: s2h-b2b-settlement-detail
    type: s2h
    spec:
      query: s2h_b2b_settlement_detail-bd27.query.sql
      targetTable: b2b_settlement_detail_raw
      targetDatabase: '{{finance_b2b_db}}'
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      enableValidationRule: false
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
      sqlConnection:
        id: sg-dwsec_-_agoda_finance_-_hadoop_it_finance_user
    dependsOn:
      - fork-5c5f
  - id: s2h-unapplied-cash-details-daily-79b8
    name: s2h-unapplied-cash-details-daily
    type: s2h
    spec:
      query: s2h_unapplied_cash_details_daily-79b8.query.sql
      targetTable: unapplied_cash_details
      targetDatabase: '{{finance_b2b_db}}'
      partitionBy:
        staticValue:
          columns:
            - name: master
              type: string
              value: master
      enableValidationRule: false
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
      sqlConnection:
        id: sg-dwsec_-_agoda_finance_-_hadoop_it_finance_user
    dependsOn:
      - fork-5c5f
  - id: join-d927
    name: join-d927
    type: join
    dependsOn:
      - s2h-unapplied-cash-details-daily-79b8
      - email-b2b-settlement-detail-4a66
  - id: h2h-b2b-settlement-detail-66ff
    name: h2h-b2b-settlement-detail
    type: h2h
    spec:
      query: h2h_b2b_settlement_detail-66ff.query.sql
      targetTable: b2b_settlement_detail
      targetDatabase: '{{finance_b2b_db}}'
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      enableValidationRule: false
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
    dependsOn:
      - s2h-b2b-settlement-detail-bd27
  - id: email-b2b-settlement-detail-4a66
    name: email-b2b-settlement-detail
    type: email
    spec:
      to:
        - <EMAIL>
        - <EMAIL>
      cc: []
      bcc: []
      subject: ETL b2b_settlement_detail run pass in {{env}}
      attachments: []
      contentType: text/plain
      bodyFile: email_b2b_settlement_detail-4a66.body.txt
    dependsOn:
      - h2h-b2b-settlement-detail-66ff
  - id: email-b2b-settlement-completed-b469
    name: email-b2b-settlement-completed
    type: email
    spec:
      to:
        - <EMAIL>
        - <EMAIL>
      cc: []
      bcc: []
      subject: B2B Settlement Completed  in {{env}}
      attachments: []
      contentType: text/plain
      bodyFile: email_b2b_settlement_completed-b469.body.txt
    dependsOn:
      - join-d927
  - id: multi-product-b2b-reporter-d7f3
    name: multi-product-b2b-reporter
    type: sub-workflow
    spec:
      workflowPath: >-
        git://etl-workflows/wf-service-accounts/hk-fin-ar-prod-svc/ar-reporter#main
      subWorkflowProperties:
        - key: datadate
          value: '{{hive_today_date}}'
        - key: reporter_args
          value: '{{reporter_args}}'
        - key: tag
          value: '{{env_path}}'
        - key: config
          value: '{{reporter_multiproduct_daily_config}}'
        - key: log4j_config
          value: '{{log4j_config}}'
        - key: submit_user
          value: '{{submitUser}}'
      propagateConfigurations: true
    dependsOn:
      - email-b2b-reconciliation-completed-a886
  - id: email-b2b-finance-reporter-completed-b5a4
    name: email-b2b-finance-reporter-completed
    type: email
    spec:
      to:
        - <EMAIL>
        - <EMAIL>
      cc: []
      bcc: []
      subject: B2B Finance Reporter completed in {{env}}
      attachments: []
      contentType: text/plain
      bodyFile: email_b2b_finance_reporter_completed-b5a4.body.txt
    dependsOn:
      - multi-product-b2b-reporter-d7f3
  - id: email-b2b-reconciliation-completed-a886
    name: email-b2b-reconciliation-completed
    type: email
    spec:
      to:
        - <EMAIL>
        - <EMAIL>
      cc: []
      bcc: []
      subject: B2B Reconciliation completed in {{env}}
      attachments: []
      contentType: text/plain
      bodyFile: email_b2b_reconciliation_completed-a886.body.txt
    dependsOn:
      - generic-recon-b2b-multi-product-ar-reconicliation-daily-jjgc
  - id: generic-recon-b2b-multi-product-ar-reconicliation-daily-jjgc
    name: generic-recon-b2b-multi-product-ar-reconicliation-daily
    maxRetry: 0
    dependsOn:
      - email-b2b-settlement-completed-b469
    spec:
      mainApplicationFile: local:///opt/app/assembly/assembly.jar
      image: reg-sg.agodadev.io/gitlab/financeplatform/reconciliation-platform/reconciliation-platform/accountreceivablereconciliator
      tag: release
      additionalSparkConfigurations:
        - key: spark.serializer
          value: org.apache.spark.serializer.KryoSerializer
        - key: spark.hadoop.hadoop.security.credential.provider.path
          value: ''
        - key: spark.kerberos.access.hadoopFileSystems
          value: hdfs://nameservice1,hdfs://nameservice2
        - key: spark.eventLog.enabled
          value: 'true'
        - key: spark.yarn.populateHadoopClasspath
          value: 'true'
        - key: spark.hadoop.smartloader.newtable-vast
          value: 'true'
        - key: spark.driver.maxResultSize
          value: 7g
        - key: spark.queue
          value: '{{queue_name}}'
        - key: spark.shuffle.service.enabled
          value: 'true'
        - key: spark.sql.hive.metastore.token.signature
          value: hiveserver2ClientToken
        - key: spark.yarn.queue
          value: '{{queue_name}}'
        - key: spark.kryoserializer.buffer.max
          value: 256m
        - key: spark.sql.parquet.int96RebaseModeInRead
          value: LEGACY
        - key: spark.hadoop.hive.execution.engine
          value: mr
        - key: spark.sql.codegen.fallback
          value: 'true'
        - key: spark.sql.hive.metastore.jars
          value: /usr/hdp/current/hive-client/lib/*:./*
        - key: spark.storage.memoryFraction
          value: '0.2'
        - key: spark.locality.wait
          value: '1'
        - key: spark.storage.memoryMapThreshold
          value: '2097152'
        - key: spark.logConf
          value: 'false'
        - key: spark.sql.broadcastTimeout
          value: '3600'
        - key: spark.yarn.maxAppAttempts
          value: '1'
        - key: spark.sql.parquet.writeLegacyFormat
          value: 'true'
        - key: spark.sql.parquet.int96RebaseModeInWrite
          value: LEGACY
        - key: hive.metastore.disallow.incompatible.col.type.changes
          value: 'false'
        - key: spark.sql.hive.metastore.version
          value: 3.1.1
      sparkVersion: 3.3.1
      mainClass: com.agoda.finance.accountreceivablereconciliator.Main
      driver:
        memoryOverhead: 16384m
        javaOptions:
          - '-XX:+CMSClassUnloadingEnabled'
          - '-XX:+HeapDumpOnOutOfMemoryError'
          - '-XX:HeapDumpPath=/agoda/heap_dump/'
          - '-Dhive.metastore.filter.hook=com.agoda.adp.hadoop.hive.client.hook.AdpFilterHook'
          - '-Dconfig.strategy=com.agoda.ml.config.MultiResourceStrategy'
          - '-Dconfig.resource={{generic_ar_b2b_config}}'
          - '-Dlog4j.configuration=log4j_adp.properties'
          - '-Dhadoop.spark.submituser={{submitUser}}'
        memory: 30g
      dynamicAllocation: {}
      executor:
        instances: '15'
        cores: '10'
        memory: 20g
        memoryOverhead: 16384m
        javaOptions:
          - '-XX:+CMSClassUnloadingEnabled'
          - '-XX:+HeapDumpOnOutOfMemoryError'
          - '-XX:HeapDumpPath=/agoda/heap_dump/'
          - '-Dconfig.strategy=com.agoda.ml.config.MultiResourceStrategy'
          - '-Dconfig.resource={{generic_ar_b2b_config}}'
          - '-Dlog4j.configuration=log4j_adp.properties'
          - '-Dhadoop.spark.submituser={{submitUser}}'
      dependencies:
        jars:
          - local:///opt/app/assembly/dependency_cache.jar
        files:
          - local:///opt/app/deps/files/log4j_adp.properties
      arguments:
        - processdate={{ hive_today_date }}  workflows=RECON,ALERT
    type: spark
    retryInterval: 1
  - id: generic-recon-kpi-b2b-multi-product-ar-reconicliation-daily-gbdf
    name: generic-recon-kpi-b2b-multi-product-ar-reconicliation-daily
    maxRetry: 0
    dependsOn:
      - email-b2b-finance-reporter-completed-b5a4
    spec:
      mainApplicationFile: local:///opt/app/assembly/assembly.jar
      image: reg-sg.agodadev.io/gitlab/financeplatform/reconciliation-platform/reconciliation-platform/accountreceivablereconciliator
      tag: release
      additionalSparkConfigurations:
        - key: spark.serializer
          value: org.apache.spark.serializer.KryoSerializer
        - key: spark.hadoop.hadoop.security.credential.provider.path
          value: ''
        - key: spark.kerberos.access.hadoopFileSystems
          value: hdfs://nameservice1,hdfs://nameservice2
        - key: spark.eventLog.enabled
          value: 'true'
        - key: spark.yarn.populateHadoopClasspath
          value: 'true'
        - key: spark.hadoop.smartloader.newtable-vast
          value: 'true'
        - key: spark.driver.maxResultSize
          value: 7g
        - key: spark.queue
          value: '{{queue_name}}'
        - key: spark.shuffle.service.enabled
          value: 'true'
        - key: spark.sql.hive.metastore.token.signature
          value: hiveserver2ClientToken
        - key: spark.yarn.queue
          value: '{{queue_name}}'
        - key: spark.kryoserializer.buffer.max
          value: 256m
        - key: spark.sql.parquet.int96RebaseModeInRead
          value: LEGACY
        - key: spark.hadoop.hive.execution.engine
          value: mr
        - key: spark.sql.codegen.fallback
          value: 'true'
        - key: spark.sql.hive.metastore.jars
          value: /usr/hdp/current/hive-client/lib/*:./*
        - key: spark.storage.memoryFraction
          value: '0.2'
        - key: spark.locality.wait
          value: '1'
        - key: spark.storage.memoryMapThreshold
          value: '2097152'
        - key: spark.logConf
          value: 'false'
        - key: spark.sql.broadcastTimeout
          value: '3600'
        - key: spark.yarn.maxAppAttempts
          value: '1'
        - key: spark.sql.parquet.writeLegacyFormat
          value: 'true'
        - key: spark.sql.parquet.int96RebaseModeInWrite
          value: LEGACY
        - key: hive.metastore.disallow.incompatible.col.type.changes
          value: 'false'
        - key: spark.sql.hive.metastore.version
          value: 3.1.1
      sparkVersion: 3.3.1
      mainClass: com.agoda.finance.accountreceivablereconciliator.Main
      driver:
        memoryOverhead: 16384m
        javaOptions:
          - '-XX:+CMSClassUnloadingEnabled'
          - '-XX:+HeapDumpOnOutOfMemoryError'
          - '-XX:HeapDumpPath=/agoda/heap_dump/'
          - '-Dhive.metastore.filter.hook=com.agoda.adp.hadoop.hive.client.hook.AdpFilterHook'
          - '-Dconfig.strategy=com.agoda.ml.config.MultiResourceStrategy'
          - '-Dconfig.resource={{generic_ar_b2b_config}}'
          - '-Dlog4j.configuration=log4j_adp.properties'
          - '-Dhadoop.spark.submituser={{submitUser}}'
        memory: 30g
      dynamicAllocation: {}
      executor:
        instances: '15'
        cores: '10'
        memory: 20g
        memoryOverhead: 16384m
        javaOptions:
          - '-XX:+CMSClassUnloadingEnabled'
          - '-XX:+HeapDumpOnOutOfMemoryError'
          - '-XX:HeapDumpPath=/agoda/heap_dump/'
          - '-Dconfig.strategy=com.agoda.ml.config.MultiResourceStrategy'
          - '-Dconfig.resource={{generic_ar_b2b_config}}'
          - '-Dlog4j.configuration=log4j_adp.properties'
          - '-Dhadoop.spark.submituser={{submitUser}}'
      dependencies:
        jars:
          - local:///opt/app/assembly/dependency_cache.jar
        files:
          - local:///opt/app/deps/files/log4j_adp.properties
      arguments:
        - processdate={{ hive_today_date }}  workflows=KPI
    type: spark
    retryInterval: 1
params_profile:
  - name: default
    params:
      - name: hive_today_date
        value: __deferred
        dataType: date
      - name: from
        value: __deferred
        dataType: date
      - name: to
        value: __deferred
        dataType: date
      - name: finance_b2b_db
        value: finance_b2b
        dataType: string
      - name: env
        value: production
        dataType: string
      - name: reporter_args
        value: slack=true
        dataType: string
      - name: reporter_multiproduct_daily_config
        value: sg/b2b/release_multiproduct_daily.conf
        dataType: string
      - name: generic_ar_b2b_config
        value: b2b/prod.conf
        dataType: string
      - name: env_path
        value: release
        dataType: string
      - name: submitUser
        value: hk-fin-ar-prod-svc
        dataType: string
      - name: subworkflow_path
        value: multiproduct_b2b_daily
        dataType: string
      - name: queue_name
        value: production
        dataType: string
      - name: log4j_config
        value: log4j_adp.properties
        dataType: string
  - name: dev
    params:
      - name: finance_b2b_db
        value: finance_b2b_dev
        dataType: string
      - name: env
        value: dev
        dataType: string
      - name: reporter_args
        value: slack=false
        dataType: string
      - name: reporter_multiproduct_daily_config
        value: sg/b2b/dev_multiproduct_daily.conf
        dataType: string
      - name: env_path
        value: master
        dataType: string
      - name: submitUser
        value: hk-fin-ar-prod-svc--dev
        dataType: string
      - name: generic_ar_b2b_config
        value: b2b/dev.conf
        dataType: string
      - name: queue_name
        value: default
        dataType: string
      - name: log4j_config
        value: log/log4j_adp_qa.properties
        dataType: string
schedules:
  - id: prod-b2b-reconciliator-9395
    name: PROD B2B Reconciliator
    timezone: Asia/Bangkok
    cron: 30 9 * * *
    parameters:
      - dataType: date
        name: hive_today_date
        value:
          dateFormat: yyyyMMdd
          offsets:
            - offset: 0
              format: Day
      - dataType: date
        name: from
        value:
          dateFormat: yyyy-MM-dd 16:00:00
          offsets:
            - offset: -3
              format: Day
      - dataType: date
        name: to
        value:
          dateFormat: yyyy-MM-dd 16:00:00
          offsets:
            - offset: 0
              format: Day
