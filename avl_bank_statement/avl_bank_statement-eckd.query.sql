WITH LATEST_UPLOAD AS (SELECT TOP 1 avl_record_id,
                                    file_name
                       FROM agoda_finance.dbo.avl_record
                       WHERE CONVERT(VARCHAR, created_when, 112) = '{{datadate}}'
                       ORDER BY created_when DESC)
SELECT D.avl_record_detail_id,
       D.avl_record_id,
       A.file_name,
       D.bank_name,
       D.currency_code,
       D.account_code,
       CONVERT(VARCHAR(10), D.value_date, 120) + ' 00:00:00' AS value_date,
       D.amount,
       D.created_by,
       CONVERT(VARCHAR(19), D.created_when, 120)             AS created_when,
       D.modified_by,
       CONVERT(VARCHAR(19), D.modified_when, 120)            AS modified_when,
       {{datadate}}                                          AS datadate
FROM agoda_finance.dbo.avl_record_detail D
         INNER JOIN LATEST_UPLOAD A
                    ON D.avl_record_id = A.avl_record_id
WHERE $CONDITIONS