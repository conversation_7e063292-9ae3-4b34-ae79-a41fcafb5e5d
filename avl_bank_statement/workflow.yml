workflow:
  id: avl_bank_statement
  name: AVL_Bank_Statement
  description: >-
    Owner: <EMAIL>; Purpose: migrate avl bank statement data from sql to hadoop; is_temporary: No; Created by: <EMAIL>; Updated by: <EMAIL>;
  version: 2
actions:
  - id: avl-bank-statement-eckd
    name: avl_bank_statement
    type: s2h
    spec:
      query: avl_bank_statement-eckd.query.sql
      targetTable: '{{resolve_table_name("avl_bank_statement")}}'
      targetDatabase: finance_avl
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: int
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        overwritePartition: {}
      sqlConnection:
        id: sgfinancestandby_-_agoda_finance_-_finance_platform_rw_user
    dependsOn: []
  - id: avl-bank-statement-mail-bjdl
    name: avl_bank_statement_mail
    type: email
    spec:
      to:
        - <EMAIL>
      cc: []
      bcc: []
      subject: Successfully Passed AVL Bank Statement
      attachments: []
      contentType: text/plain
      bodyFile: avl_bank_statement_mail-bjdl.body.txt
    dependsOn:
      - avl-bank-statement-eckd
params_profile:
  - name: default
    params:
      - name: datadate
        value: __deferred
        dataType: string
  - name: dev
    params: []
schedules: []
