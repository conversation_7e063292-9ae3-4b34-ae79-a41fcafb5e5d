with total_invoiced as (
select	booking_id 
, sum(shared_margin_amount_invoice_currency) as total_previous_shared_margin_amount_invoice_currency
, sum(shared_margin_amount_data_feed_currency) as total_previous_shared_margin_amount_data_feed_currency
, sum(shared_margin_amount_functional_currency) as total_previous_shared_margin_amount_functional_currency
, sum(discount_amount_invoice_currency) as total_previous_discount_amount_invoice_currency
, sum(discount_amount_data_feed_currency) as total_previous_discount_amount_data_feed_currency
, sum(discount_amount_functional_currency) as total_previous_discount_amount_functional_currency
, sum(amount_due_invoice_currency) as total_previous_amount_due_invoice_currency
, sum(amount_due_data_feed_currency) as total_previous_amount_due_data_feed_currency
, sum(amount_due_functional_currency) as total_previous_amount_due_functional_currency
, sum(invoiced_customer_payment) as total_invoiced_customer_payment
, sum(invoiced_customer_payment_usd) as total_invoiced_customer_payment_usd
, sum(invoiced_supplier_margin_customer) as total_invoiced_supplier_margin_customer
, sum(invoiced_supplier_margin_usd) as total_invoiced_supplier_margin_usd
from finance_b2b.invoiced_transactions_by_invoice_date 
group by booking_id 
)
SELECT v.booking_id,
CAST(max(v.cid) as string) as cid,
max(v.booking_date) as booking_date,
max(v.start_date) as start_date,
max(v.end_date) as end_date,
max(v.sub_supplier_id) as sub_supplier_id,
max(v.service_origin_id) as service_origin_id,
max(c.country_iso2) as service_origin_iso2,
max(v.service_origin_iso3) as service_origin_iso3,
max(v.customer_payment_currency) as customer_payment_currency,
CAST(null as DECIMAL(18,8)) as fx_customer_payment_rate,
max(v.tracking_tag) as tracking_tag,
max(v.merchant_of_record_entity) as merchant_of_record_entity,
max(v.merchant_of_record_entity_type) as merchant_of_record_entity_type,
max(v.revenue_entity) as revenue_entity,
max(v.revenue_entity_type) as revenue_entity_type,
max(v.rate_contract_entity) as rate_contract_entity,
max(v.rate_contract_entity_type) as rate_contract_entity_type,
CAST(sum(v.customer_payment) as DECIMAL(18,8)) as total_customer_payment,
CAST(sum(v.customer_payment_usd) as DECIMAL(18,8)) as total_customer_payment_usd,
CAST(sum(v.supplier_margin_customer) as DECIMAL(18,8)) as total_supplier_margin_customer,
CAST(sum(v.supplier_margin_usd) as DECIMAL(18,8)) as total_supplier_margin_usd,
CAST(sum(v.supplier_processing_fee_customer) as DECIMAL(18,8)) as total_supplier_processing_fee_customer,
CAST(sum(v.supplier_processing_fee_usd) as DECIMAL(18,8)) as total_supplier_processing_fee_usd,
CAST(sum(v.supplier_tax_customer) as DECIMAL(18,8)) as total_supplier_tax_customer,
CAST(sum(v.supplier_tax_usd) as DECIMAL(18,8)) as total_supplier_tax_usd,
CAST(sum(v.supplier_tax_pay_to_government_customer) as DECIMAL(18,8)) as total_supplier_tax_pay_to_government_customer,
CAST(sum(v.supplier_tax_pay_to_government_usd) as DECIMAL(18,8)) as total_supplier_tax_pay_to_government_usd,
CAST(COALESCE(MAX(invoiced.total_previous_shared_margin_amount_invoice_currency), 0) as DECIMAL(18,8)) as total_previous_shared_margin_amount_invoice_currency,
CAST(COALESCE(MAX(invoiced.total_previous_shared_margin_amount_data_feed_currency), 0) as DECIMAL(18,8)) as total_previous_shared_margin_amount_data_feed_currency,
CAST(COALESCE(MAX(invoiced.total_previous_shared_margin_amount_functional_currency), 0) as DECIMAL(18,8)) as total_previous_shared_margin_amount_functional_currency,
CAST(COALESCE(MAX(invoiced.total_previous_amount_due_invoice_currency), 0) as DECIMAL(18,8)) as total_previous_amount_due_invoice_currency,
CAST(COALESCE(MAX(invoiced.total_previous_amount_due_data_feed_currency), 0) as DECIMAL(18,8)) as total_previous_amount_due_data_feed_currency,
CAST(COALESCE(MAX(invoiced.total_previous_amount_due_functional_currency), 0) as DECIMAL(18,8)) as total_previous_amount_due_functional_currency,
CAST(COALESCE(MAX(invoiced.total_invoiced_customer_payment), 0) as DECIMAL(18,8)) as total_invoiced_customer_payment,
CAST(COALESCE(MAX(invoiced.total_invoiced_customer_payment_usd), 0) as DECIMAL(18,8)) as total_invoiced_customer_payment_usd,
CAST(COALESCE(MAX(invoiced.total_invoiced_supplier_margin_customer), 0) as DECIMAL(18,8)) as total_invoiced_supplier_margin_customer,
CAST(COALESCE(MAX(invoiced.total_invoiced_supplier_margin_usd), 0) as DECIMAL(18,8)) as total_invoiced_supplier_margin_usd,
max(v.whitelabel_id) as whitelabel_id,
CAST(20 as int) as payment_type_id,
CAST(3 as int) as transaction_type,
max(v.payment_model) as payment_model,
max(v.product_type) as product_type,
max(v.affiliate_model) as affiliate_model,
max(CAST(COALESCE(h.state_id, 0) as bigint)) as state_id,
CASE
               WHEN max(v.revenue_entity) = 5659 THEN 5632
               ELSE max(v.revenue_entity)
END as calculated_revenue_entity,
CAST(null as int) as keep_until_processing_datadate,
max(v.whitelabel_group_id) as whitelabel_group_id,
CAST(COALESCE(MAX(invoiced.total_previous_discount_amount_invoice_currency), 0) as DECIMAL(18,8)) as total_previous_discount_amount_invoice_currency,
CAST(COALESCE(MAX(invoiced.total_previous_discount_amount_data_feed_currency), 0) as DECIMAL(18,8)) as total_previous_discount_amount_data_feed_currency,
CAST(COALESCE(MAX(invoiced.total_previous_discount_amount_functional_currency), 0) as DECIMAL(18,8)) as total_previous_discount_amount_functional_currency,
CAST(0 as DECIMAL(18,8)) as total_supplier_ess_customer,
CAST(0 as DECIMAL(18,8)) as total_supplier_ess_usd,
CAST(0 as DECIMAL(18,8)) as total_invoiced_supplier_ess_customer,
CAST(0 as DECIMAL(18,8)) as total_invoiced_supplier_ess_usd,
CAST(20240205 as int) as datadate
FROM finance_multiproduct.financial_transactions_by_booking_date v
LEFT OUTER JOIN total_invoiced as invoiced on v.booking_id = invoiced.booking_id
LEFT OUTER JOIN bi_dw.dim_country c on v.service_origin_iso3 = c.country_iso
LEFT OUTER JOIN bi_dw.dim_hotel_static h on v.sub_supplier_id = h.hotel_id
WHERE v.booking_id in (404623515,
405264639,
409824115,
589324394,
627555510,
399249763,
400022051,
554729422,
574723162,
677429102,
690456234,
1111824376,
558645518,
590573502,
602482850,
628532458,
632363602,
1084960900,
1110642024,
1114595300,
401724335,
580289194,
586352726,
596843278,
631605426,
1114520056,
407411383,
689582486,
1115334580,
406563575,
578571978,
586979806,
966877284,
1115467132,
409771019,
408600335,
562034438,
572459822,
624295638,
1111458532,
390481195,
408947247,
410068791,
410088491,
622318134,
1114575624,
602943046,
661601546,
686653286,
395703827,
614198614,
1103519840,
405837079,
409551879,
410202751,
562170746,
581207650,
1108722092,
1115557224,
628305566,
402182283,
409635495,
587002618,
409856155,
554700734,
554837102,
1112463300,
586982326,
587936706,
623338770,
1104696908,
1104835672,
1111005280,
1111217324,
1113140872,
1137535909,
408527147,
595645242,
1114574012,
567818410,
576072202,
597159646,
687834382,
690559230,
1099275768,
1114715308,
1114949336,
374650115,
405915003,
570380890,
665836398,
685080638,
375520699,
407637631,
410065119,
545895002,
576130150,
1112462508,
1113271164,
399214043,
409600811,
561405314,
600234610,
631584530,
1088092336,
1095515468,
1114257264,
406531971,
409189503,
409832651,
493007270,
582600070,
1110532292,
690061290,
1148282001,
395703811,
400729751,
1107230448,
1113446716,
546061282,
575968062,
576133594,
627573666,
1013505004,
1089329996)
GROUP BY v.booking_id