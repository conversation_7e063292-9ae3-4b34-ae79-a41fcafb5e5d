workflow:
  id: backfill-uninvoiced_transactions-20240202
  name: backfill uninvoiced_transactions 20240202
  description: ''
  version: 2
actions:
  - id: backfill-glag
    name: backfill
    type: h2h
    spec:
      query: backfill-glag.query.sql
      targetTable: uninvoiced_transactions
      targetDatabase: finance_b2b
      partitionBy:
        columnBased:
          columns:
            - name: datadate
              type: source
      enableValidationRule: true
      trackingColumns: []
      mergeBy:
        append: {}
      alterTableSchema: true
      maxDuration: 3600
    dependsOn: []
    maxRetry: '0'
params_profile:
  - name: default
    params: []
  - name: dev
    params: []
schedules: []
